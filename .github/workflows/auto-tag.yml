name: Auto Tag

on:
  push:
    branches: [ "main" ]
    paths: [ "build.gradle.kts" ]

jobs:
  auto-tag:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Extract version from build.gradle.kts
      id: get_version
      run: |
        VERSION=$(grep 'version = ' build.gradle.kts | sed 's/.*version = "\(.*\)".*/\1/')
        echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
        echo "Current version: $VERSION"

    - name: Check if tag exists
      id: check_tag
      run: |
        if git rev-parse "v${{ steps.get_version.outputs.VERSION }}" >/dev/null 2>&1; then
          echo "TAG_EXISTS=true" >> $GITHUB_OUTPUT
        else
          echo "TAG_EXISTS=false" >> $GITHUB_OUTPUT
        fi

    - name: Create tag
      if: steps.check_tag.outputs.TAG_EXISTS == 'false' && !contains(steps.get_version.outputs.VERSION, 'SNAPSHOT')
      run: |
        git config user.name github-actions
        git config user.email <EMAIL>
        git tag v${{ steps.get_version.outputs.VERSION }}
        git push origin v${{ steps.get_version.outputs.VERSION }}

    - name: Set up JDK 21
      if: steps.check_tag.outputs.TAG_EXISTS == 'false' && !contains(steps.get_version.outputs.VERSION, 'SNAPSHOT')
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Setup Gradle
      if: steps.check_tag.outputs.TAG_EXISTS == 'false' && !contains(steps.get_version.outputs.VERSION, 'SNAPSHOT')
      uses: gradle/actions/setup-gradle@af1da67850ed9a4cedd57bfd976089dd991e2582

    - name: Publish to Nexus
      if: steps.check_tag.outputs.TAG_EXISTS == 'false' && !contains(steps.get_version.outputs.VERSION, 'SNAPSHOT')
      run: ./gradlew publish
      env:
        NEXUS_USERNAME: ${{ secrets.NEXUS_USERNAME }}
        NEXUS_PASSWORD: ${{ secrets.NEXUS_PASSWORD }}

    - name: Create GitHub Release
      if: steps.check_tag.outputs.TAG_EXISTS == 'false' && !contains(steps.get_version.outputs.VERSION, 'SNAPSHOT')
      run: |
        gh release create v${{ steps.get_version.outputs.VERSION }} \
          --title "Release v${{ steps.get_version.outputs.VERSION }}" \
          --notes "Auto-generated release for version ${{ steps.get_version.outputs.VERSION }}" \
          --latest
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
