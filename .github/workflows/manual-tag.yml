name: Manual Tag

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to tag (e.g., 1.0.0)'
        required: true
        type: string

jobs:
  create-tag:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
    - uses: actions/checkout@v4

    - name: Update version in build.gradle.kts
      run: |
        sed -i 's/version = "[^"]*"/version = "${{ github.event.inputs.version }}"/' build.gradle.kts

    - name: Commit version update
      run: |
        git config user.name github-actions
        git config user.email <EMAIL>
        git add build.gradle.kts
        git commit -m "Bump version to ${{ github.event.inputs.version }}"
        git push

    - name: Create tag
      run: |
        git tag v${{ github.event.inputs.version }}
        git push origin v${{ github.event.inputs.version }}