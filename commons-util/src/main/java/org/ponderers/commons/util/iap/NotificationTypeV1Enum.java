package org.ponderers.commons.util.iap;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <a href="https://developer.apple.com/documentation/appstoreservernotifications/notification_type">https://developer.apple.com/documentation/appstoreservernotifications/notification_type</a>
 *
 * <AUTHOR>
 */
@Getter
@ToString
@AllArgsConstructor
public enum NotificationTypeV1Enum {

    DID_RENEW("DID_RENEW", ""),
    CANCEL("CANCEL", ""),
    DID_CHANGE_RENEWAL_PREF("DID_CHANGE_RENEWAL_PREF", ""),
    DID_CHANGE_RENEWAL_STATUS("DID_CHANGE_RENEWAL_STATUS", ""),
    DID_FAIL_TO_RENEW("DID_FAIL_TO_RENEW", ""),
    DID_RECOVER("DID_RECOVER", ""),
    INITIAL_BUY("INITIAL_BUY", ""),
    INTERACTIVE_RENEWAL("INTERACTIVE_RENEWAL", ""),
    REN<PERSON>WAL("RENEWAL", ""),
    REFUND("REFUND", ""),
    CONSUMPTION_REQUEST("CONSUMPTION_REQUEST", ""),
    ;

    private final String code;
    private final String desc;

}
