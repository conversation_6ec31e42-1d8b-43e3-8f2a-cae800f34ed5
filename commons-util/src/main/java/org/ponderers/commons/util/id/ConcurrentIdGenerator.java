package org.ponderers.commons.util.id;

import com.google.common.base.Preconditions;
import lombok.SneakyThrows;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLongFieldUpdater;
import java.util.function.Supplier;

/**
 * <pre>
 * +-----+--------------------------+------------+--------------+
 * |正数位|时间序列位                  |机器码位     |序列号         |
 * +-----+--------------------------+------------+--------------+
 * 1bit    41bit                     9bit        13bit
 * 9bit位机器码支持512台机器   0-511
 * 13bit支持8192个序列生成，即1毫秒支持8192次生成
 * id生成器
 * </pre>
 *
 * <AUTHOR>
 */
public class ConcurrentIdGenerator implements IdGenerator {

    private static final int NODE_SHIFT = 9;
    private static final int SEQ_SHIFT = 13;
    private static final short MAX_SEQUENCE = 1 << SEQ_SHIFT;
    /**
     * 毫秒更新
     */
    private static final AtomicLongFieldUpdater<ConcurrentIdGenerator> millisUpdate = AtomicLongFieldUpdater
            .newUpdater(ConcurrentIdGenerator.class, "idMillis");
    /**
     * 机器id
     * 机器id最大支持到 1 << 9 个机器位
     */
    private final int machineId;
    /**
     * 时间偏移量
     */
    private final long timeOffset;
    /**
     * millis SequenceId
     * 毫秒的序列id
     * 最大支持到0-MAX_SEQUENCE
     */
    private final AtomicInteger sequenceInteger = new AtomicInteger(0);
    /**
     * 当前ID时间戳
     */
    private volatile long idMillis = 0;
    /**
     * 时间函数
     */
    private final Supplier<Long> timeSupplier;

    /**
     * 使用默认相对时间
     * 时间偏移量为0，则默认使用1970年
     *
     * @param machineId
     */
    public ConcurrentIdGenerator(int machineId) {
        this(machineId, 0L);
    }

    /**
     * 使用默认相对时间
     *
     * @param machineId
     */
    public ConcurrentIdGenerator(int machineId, long timeOffset) {
        this(machineId, timeOffset, System::currentTimeMillis);
    }

    /**
     * 使用默认相对时间
     *
     * @param machineId
     */
    public ConcurrentIdGenerator(int machineId, long timeOffset, Supplier<Long> timeSupplier) {
        Preconditions.checkArgument(machineId > 0, "machineId must be positive");
        Preconditions.checkArgument(machineId < (1 << NODE_SHIFT), "machineId Illegal state");
        Preconditions.checkNotNull(timeSupplier, "TimeProvider must not be empty");
        this.machineId = machineId;
        this.timeOffset = timeOffset;
        this.timeSupplier = timeSupplier;
    }

    /**
     * id生成器
     * 无锁的id生成器
     *
     * @return
     * @throws IllegalStateException
     */
    @SneakyThrows
    public synchronized long nextId() {
        final long currentMillis = (this.timeSupplier.get() - timeOffset);
        // 重置timestamp
        if (currentMillis > idMillis && millisUpdate.compareAndSet(this, idMillis, currentMillis)) {
            sequenceInteger.set(0);
        }
        // 计算sequence
        int count = sequenceInteger.incrementAndGet();
        if (count >= MAX_SEQUENCE) {
            TimeUnit.MICROSECONDS.sleep(1L);
            return nextId();
        }
        return currentMillis << NODE_SHIFT << SEQ_SHIFT | (long) machineId << SEQ_SHIFT | count;
    }
}
