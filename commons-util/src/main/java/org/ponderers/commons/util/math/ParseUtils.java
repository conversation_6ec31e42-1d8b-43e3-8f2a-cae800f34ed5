package org.ponderers.commons.util.math;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

public class ParseUtils {

    private static final Logger log = LoggerFactory.getLogger(ParseUtils.class);

    private ParseUtils() {
    }

    public static int tryParseInt(String str, int defaultValue) {
        try {
            if (StringUtils.isBlank(str) || !NumberUtils.isCreatable(str)) {
                return defaultValue;
            }
            return Integer.parseInt(str);
        } catch (Exception e) {
            log.warn("ParseUtils.tryParseInt, 解析字符串数值异常。str: {}, defaultValue: {}, errorMessage: {}",
                    str, defaultValue, ExceptionUtils.getMessage(e));
            return defaultValue;
        }
    }

    public static long tryParseLong(String str, long defaultValue) {
        try {
            if (StringUtils.isBlank(str) || !NumberUtils.isCreatable(str)) {
                return defaultValue;
            }
            return Long.parseLong(str);
        } catch (Exception e) {
            log.warn("ParseUtils.tryParseLong, 解析字符串数值异常。str: {}, defaultValue: {}, errorMessage: {}",
                    str, defaultValue, ExceptionUtils.getMessage(e));
            return defaultValue;
        }
    }

    public static BigDecimal tryParseBigDecimal(String str, BigDecimal defaultValue) {
        try {
            if (StringUtils.isBlank(str)) {
                return defaultValue;
            }
            return new BigDecimal(str);
        } catch (Exception e) {
            log.warn("ParseUtils.tryParseBigDecimal, 解析字符串为定点数异常。str: {}, defaultValue: {}, errorMessage: {}",
                    str, defaultValue, ExceptionUtils.getMessage(e));
            return defaultValue;
        }
    }

    public static Pair<Integer, Integer> longToIntPair(long i) {
        return Pair.of((int) (i >> 32), (int) (i & 0x000000ffffffffL));
    }

    public static long intPairToLong(Pair<Integer, Integer> pair) {
        return (long) pair.getLeft() << 32 | pair.getRight() & 0xFFFFFFFFL;
    }

}
