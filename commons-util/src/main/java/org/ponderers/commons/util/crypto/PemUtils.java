package org.ponderers.commons.util.crypto;

import org.apache.commons.io.IOUtils;
import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.EncodedKeySpec;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class PemUtils {

    private static final Logger log = LoggerFactory.getLogger(PemUtils.class);

    public static final int HEX = 16;

    private PemUtils() {
    }

    public static byte[] parsePEMFile(File pemFile) throws IOException {
        if (!pemFile.isFile() || !pemFile.exists()) {
            throw new FileNotFoundException(String.format("The file '%s' doesn't exist.", pemFile.getAbsolutePath()));
        }
        byte[] content;
        try (PemReader reader = new PemReader(new FileReader(pemFile))) {
            PemObject pemObject = reader.readPemObject();
            content = pemObject.getContent();
        }
        return content;
    }

    public static byte[] parsePEMFile(InputStream inputStream) throws IOException {
        byte[] content;
        try (PemReader reader = new PemReader(new InputStreamReader(inputStream))) {
            PemObject pemObject = reader.readPemObject();
            content = pemObject.getContent();
        }
        return content;
    }

    public static PublicKey getPublicKey(byte[] keyBytes, String algorithm) {
        PublicKey publicKey = null;
        try {
            KeyFactory kf = KeyFactory.getInstance(algorithm);
            EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            publicKey = kf.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException e) {
            log.warn("Could not reconstruct the public key, the given algorithm could not be found.");
        } catch (InvalidKeySpecException e) {
            log.warn("Could not reconstruct the public key");
        }

        return publicKey;
    }

    public static PrivateKey getPrivateKey(byte[] keyBytes, String algorithm) {
        PrivateKey privateKey = null;
        try {
            KeyFactory kf = KeyFactory.getInstance(algorithm);
            EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            privateKey = kf.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            log.warn("Could not reconstruct the private key, the given algorithm could not be found.");
        } catch (InvalidKeySpecException e) {
            log.warn("Could not reconstruct the private key");
        }
        return privateKey;
    }

    public static PublicKey readPublicKeyFromFile(String filepath, String algorithm) throws IOException {
        byte[] bytes = PemUtils.parsePEMFile(new File(filepath));
        return PemUtils.getPublicKey(bytes, algorithm);
    }

    public static PrivateKey readPrivateKeyFromFile(String filepath, String algorithm) throws IOException {
        byte[] bytes = PemUtils.parsePEMFile(new File(filepath));
        return PemUtils.getPrivateKey(bytes, algorithm);
    }

    public static PublicKey readPublicKeyFromInputStream(InputStream inputStream, String algorithm) throws IOException {
        byte[] bytes = PemUtils.parsePEMFile(inputStream);
        return PemUtils.getPublicKey(bytes, algorithm);
    }

    public static PrivateKey readPrivateKeyFromInputStream(InputStream inputStream, String algorithm) throws IOException {
        byte[] bytes = PemUtils.parsePEMFile(inputStream);
        return PemUtils.getPrivateKey(bytes, algorithm);
    }

    /**
     * 从字符串中加载RSA私钥。
     *
     * @param keyString 私钥字符串
     * @return RSA私钥
     */
    public static PrivateKey loadPrivateKeyFromString(String keyString) {
        try {
            keyString = keyString
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");
            return KeyFactory.getInstance("RSA")
                    .generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyString)));
        } catch (NoSuchAlgorithmException e) {
            throw new UnsupportedOperationException(e);
        } catch (InvalidKeySpecException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 从字符串中加载指定算法的私钥
     *
     * @param keyString 私钥字符串
     * @param algorithm 私钥算法
     * @param provider  the provider
     * @return 私钥
     */
    public static PrivateKey loadPrivateKeyFromString(String keyString, String algorithm, String provider) {
        try {
            keyString = keyString
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");
            return KeyFactory.getInstance(algorithm, provider)
                    .generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyString)));
        } catch (NoSuchAlgorithmException e) {
            throw new UnsupportedOperationException(e);
        } catch (InvalidKeySpecException | NoSuchProviderException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 从文件路径加载RSA私钥
     *
     * @param keyPath 私钥路径
     * @return RSA私钥
     */
    public static PrivateKey loadPrivateKeyFromPath(String keyPath) {
        return loadPrivateKeyFromString(readPrivateKeyStringFromPath(keyPath));
    }

    /**
     * 从文件路径加载指定算法的私钥
     *
     * @param keyPath   私钥路径
     * @param algorithm 私钥算法
     * @param provider  the provider
     * @return 私钥
     */
    public static PrivateKey loadPrivateKeyFromPath(String keyPath, String algorithm, String provider) {
        return loadPrivateKeyFromString(readPrivateKeyStringFromPath(keyPath), algorithm, provider);
    }

    private static String readPrivateKeyStringFromPath(String keyPath) {
        try (FileInputStream inputStream = new FileInputStream(keyPath)) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 从输入流加载X.509证书
     *
     * @param inputStream 私钥输入流
     * @return X.509证书
     */
    public static X509Certificate loadX509FromStream(InputStream inputStream) {
        try {
            return (X509Certificate)
                    CertificateFactory.getInstance("X.509").generateCertificate(inputStream);
        } catch (CertificateException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 从输入流加载X.509证书
     *
     * @param inputStream 私钥输入流
     * @param provider    the provider
     * @return X.509证书
     */
    public static X509Certificate loadX509FromStream(InputStream inputStream, String provider) {
        try {
            return (X509Certificate)
                    CertificateFactory.getInstance("X.509", provider).generateCertificate(inputStream);
        } catch (CertificateException | NoSuchProviderException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * 从文件路径加载X.509证书
     *
     * @param certificatePath 证书文件路径
     * @return X.509证书
     */
    public static X509Certificate loadX509FromPath(String certificatePath) {
        try (FileInputStream inputStream = new FileInputStream(certificatePath)) {
            return loadX509FromStream(inputStream);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 从文件路径加载X.509证书
     *
     * @param certificatePath 证书文件路径
     * @param provider        the provider
     * @return X.509证书
     */
    public static X509Certificate loadX509FromPath(String certificatePath, String provider) {
        try (FileInputStream inputStream = new FileInputStream(certificatePath)) {
            return loadX509FromStream(inputStream, provider);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 从字符串加载X.509证书
     *
     * @param certificateString 证书字符串
     * @return X.509证书
     */
    public static X509Certificate loadX509FromString(String certificateString) {
        try (ByteArrayInputStream inputStream =
                     new ByteArrayInputStream(certificateString.getBytes(StandardCharsets.UTF_8))) {
            return loadX509FromStream(inputStream);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 从字符串加载X.509证书
     *
     * @param certificateString 证书字符串
     * @param provider          the provider
     * @return X.509证书
     */
    public static X509Certificate loadX509FromString(String certificateString, String provider) {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(certificateString.getBytes(StandardCharsets.UTF_8))) {
            return loadX509FromStream(inputStream, provider);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static String getSerialNumber(X509Certificate certificate) {
        return certificate.getSerialNumber().toString(HEX).toUpperCase();
    }

}
