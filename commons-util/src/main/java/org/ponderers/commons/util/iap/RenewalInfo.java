package org.ponderers.commons.util.iap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewalInfo {
    @JsonProperty("environment")
    private String environment;
    @JsonProperty("recentSubscriptionStartDate")
    private long recentSubscriptionStartDate;
    @JsonProperty("originalTransactionId")
    private String originalTransactionId;
    @JsonProperty("productId")
    private String productId;
    @JsonProperty("autoRenewStatus")
    private int autoRenewStatus;
    @JsonProperty("autoRenewProductId")
    private String autoRenewProductId;
    @JsonProperty("signedDate")
    private long signedDate;
    @JsonProperty("renewalDate")
    private long renewalDate;
}