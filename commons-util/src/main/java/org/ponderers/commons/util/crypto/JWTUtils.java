package org.ponderers.commons.util.crypto;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;

/**
 * JSON Web Token(RFC7519)
 *
 * <AUTHOR>
 * @see <a href="https://github.com/tymondesigns/jwt-auth/wiki/Creating-Tokens">tymondesigns/jwt-auth</a>
 */
public class JWTUtils {

    /**
     * Create Json Web Token
     */
    public static String createToken(String subject, String secret) {
        return createToken(subject, secret, StringUtils.EMPTY);
    }

    /**
     * Create Json Web Token
     *
     * @param subject 用户ID
     * @return token Json Web Token
     */
    public static String createToken(String subject, String secret, String issuer) {
        Date now = new Date();
        Algorithm algorithm = Algorithm.HMAC256(secret);
        String jti = DigestUtils.md5Hex(issuer + now.getTime());
        return JWT.create()
                .withJWTId(jti)
                .withSubject(subject)
                .withIssuer(issuer)
                .withIssuedAt(now)
                .withNotBefore(now)
                .withExpiresAt(DateUtils.addHours(now, 1))
                .sign(algorithm);
    }

    /**
     * Parse Json Web Token
     *
     * @param token Json Web Token
     * @return subject 用户ID
     */
    public static String parseToken(String token, String secret, String issuer) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        JWTVerifier verifier = JWT.require(algorithm).withIssuer(issuer).build();
        DecodedJWT decodedJWT = verifier.verify(token);
        return decodedJWT.getSubject();
    }

    /**
     * Parse Json Web Token
     *
     * @param token Json Web Token
     * @return subject 用户ID
     */
    public static String parseToken(String token, String secret) {
        return parseToken(token, secret, StringUtils.EMPTY);
    }

}
