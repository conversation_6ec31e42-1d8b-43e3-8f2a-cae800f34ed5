package org.ponderers.commons.util.http;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.impl.DefaultHttpRequestRetryStrategy;
import org.apache.hc.client5.http.impl.DefaultRedirectStrategy;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.TrustSelfSignedStrategy;
import org.apache.hc.core5.http.*;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.apache.hc.core5.http.message.StatusLine;
import org.apache.hc.core5.net.URIBuilder;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * While HttpClient instances are thread safe and can be shared between multiple threads of execution,
 * it is highly recommended that each thread maintains its own dedicated instance of HttpContext .
 *
 * <AUTHOR>
 */
public class ApacheHttp5Utils {

    private static final Logger log = LoggerFactory.getLogger(ApacheHttp5Utils.class);

    public static final int AVAILABLE_PROCESSORS = Runtime.getRuntime().availableProcessors();

    private ApacheHttp5Utils() {
    }

    public static CloseableHttpClient createClient() {
        return createClient(500, 1000, 3000);
    }

    public static CloseableHttpClient createClient(int connectionRequestTimeout, int connectTimeout, int socketTimeout) {
        return createClient(connectionRequestTimeout, connectTimeout, socketTimeout, Proxy.NO_PROXY);
    }

    public static CloseableHttpClient createClient(int connectionRequestTimeout, int connectTimeout, int socketTimeout, Proxy proxy) {
        final PoolingHttpClientConnectionManager connectionManager = buildConnectionManager(connectTimeout, socketTimeout);
        final RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(Timeout.ofMilliseconds(connectionRequestTimeout))
                .build();
        HttpClientBuilder httpClientBuilder = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setProxy(convertToHttpHost(proxy))
                .setRedirectStrategy(new DefaultRedirectStrategy())
                .setRetryStrategy(new DefaultHttpRequestRetryStrategy(3, TimeValue.ofMilliseconds(50)));
        if (Objects.nonNull(connectionManager.getTotalStats())) {
            log.info("Pool statistics: {}", connectionManager.getTotalStats());
        }
        return httpClientBuilder.build();
    }

    private static HttpHost convertToHttpHost(Proxy proxy) {
        if (Proxy.Type.DIRECT.equals(proxy.type())) return null;
        InetSocketAddress address = (InetSocketAddress) proxy.address();
        return new HttpHost(address.getHostName(), address.getPort());
    }

    private static PoolingHttpClientConnectionManager buildConnectionManager(int connectTimeout, int socketTimeout) {
        try {
            // 配置同时支持 HTTP 和 HTTPS
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, new TrustSelfSignedStrategy())
                    .build();
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", new SSLConnectionSocketFactory(sslContext))
                    .build();
            PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            // 配置默认连接配置
            connManager.setDefaultConnectionConfig(ConnectionConfig.custom()
                    .setConnectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .setSocketTimeout(socketTimeout, TimeUnit.MILLISECONDS)
                    .build());
            // 配置每个链路的最大线程数
            connManager.setDefaultMaxPerRoute(AVAILABLE_PROCESSORS * 4);
            // 配置整个连接池最大线程数
            connManager.setMaxTotal(connManager.getDefaultMaxPerRoute() * 2);
            return connManager;
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            throw new ContextedRuntimeException("buildConnectionManager() invoke failure", e);
        }
    }

    @SneakyThrows
    public static InputStream httpGetSteam(final CloseableHttpClient httpClient, String url) {
        final HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = httpClient.execute(httpGet);
        final HttpEntity entity = response.getEntity();
        return Objects.nonNull(entity) ? entity.getContent() : null;
    }

    @SneakyThrows
    public static Optional<String> syncGet(CloseableHttpClient httpClient, HttpRequest httpRequest) {
        URIBuilder uriBuilder = new URIBuilder(httpRequest.getUrl());
        httpRequest.getQueryParameters().forEach(uriBuilder::addParameter);
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        httpRequest.getHeaders().forEach(httpGet::setHeader);
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            HttpEntity entity = response.getEntity();
            String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            EntityUtils.consumeQuietly(entity);
            return Optional.ofNullable(content);
        } catch (Exception e) {
            log.error("调用[syncGet]方法，调用异常。httpRequest: {}", httpRequest, e);
            return Optional.empty();
        }
    }

    /**
     * 请求URL资源
     * <p>
     * 首次请求失败后，重试3次，每次重试时间间隔为1s、2s、4s
     *
     * @param url 请求资源URL
     * @return 资源流
     */
    public static InputStream httpGetSteam(String url) {
        try {
            URL oUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) oUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            conn.setReadTimeout(50 * 1000);
            return conn.getInputStream();
        } catch (IOException e) {
            log.error("HTTP GET STREAM ERROR url: {}", url, e);
        }
        return null;
    }

    @SneakyThrows
    public static Optional<String> syncPostFormBody(CloseableHttpClient httpClient, HttpRequest httpRequest) {
        try {
            HttpPost httpPost = new HttpPost(httpRequest.getUrl());
            httpPost.addHeader("Content-type", "application/x-www-form-urlencoded; charset=utf-8");
            httpRequest.getHeaders().forEach(httpPost::addHeader);
            List<NameValuePair> nameValuePairs = httpRequest.getBodyParams().entrySet().stream()
                    .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue())).
                    collect(Collectors.toList());
            httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, StandardCharsets.UTF_8));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            if (!isSuccessful(response) || Objects.isNull(response.getEntity())) {
                log.error("调用[syncPostFormBody]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            return Optional.of(EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("调用[syncPostFormBody]方法，调用异常。httpRequest: {}", httpRequest, e);
            return Optional.empty();
        }
    }

    private static boolean isSuccessful(HttpResponse response) {
        if (response == null) return false;
        StatusLine statusLine = new StatusLine(response);
        int statusCode = statusLine.getStatusCode();
        return statusCode >= HttpStatus.SC_OK && statusCode < HttpStatus.SC_MULTIPLE_CHOICES;
    }

    public static Optional<String> syncPostJsonBody(final CloseableHttpClient httpClient, HttpRequest httpRequest) {
        try {
            HttpPost httpPost = new HttpPost(httpRequest.getUrl());
            ContentType contentType = ContentType.create("application/json", "");
            httpPost.addHeader("Content-Type", contentType.toString());
            httpRequest.getHeaders().forEach(httpPost::addHeader);
            httpPost.setEntity(new StringEntity(httpRequest.getJsonBody(), StandardCharsets.UTF_8));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            if (!isSuccessful(response) || Objects.isNull(response.getEntity())) {
                log.error("调用[syncPostJsonBody]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            HttpEntity httpEntity = response.getEntity();
            String body = EntityUtils.toString(httpEntity);
            EntityUtils.consumeQuietly(httpEntity);
            return Optional.ofNullable(StringUtils.trimToEmpty(body));
        } catch (Exception e) {
            log.error("调用[syncPostJsonBody]方法，调用异常。httpRequest: {}", httpRequest, e);
            return Optional.empty();
        }
    }

}


