package org.ponderers.commons.util.experimental;

import com.samskivert.mustache.Mustache;
import com.samskivert.mustache.Template;

import java.io.StringWriter;
import java.text.MessageFormat;
import java.util.Map;
import java.util.regex.Pattern;

public class TemplateUtils {

    private TemplateUtils() {
    }

    public static String format(String template, Object... data) {
        int i = 0;
        while (template.contains("{}")) {
            template = template.replaceFirst(Pattern.quote("{}"), "{" + i++ + "}");
        }
        return MessageFormat.format(template, data);
    }

    public static String render(String template, Map<String, Object> context) {
        Mustache.Compiler compiler = Mustache.compiler();
        Template tmpl = compiler.compile(template);
        StringWriter writer = new StringWriter();
        tmpl.execute(context, writer);
        return writer.toString();
    }
}
