package org.ponderers.commons.util.experimental;

import java.util.HashMap;

public class Payload extends HashMap<String, Object> {

    public static Payload createDefault() {
        return Payload.builder().build();
    }

    public static PayloadBuilder builder() {
        return new PayloadBuilder();
    }

    public static final class PayloadBuilder {
        private final Payload payload;

        public PayloadBuilder() {
            payload = new Payload();
        }

        public PayloadBuilder map(String q, Object w) {
            payload.put(q, w);
            return this;
        }

        public Payload build() {
            return payload;
        }
    }

}
