package org.ponderers.commons.util.crypto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.codec.digest.MessageDigestAlgorithms;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.function.BiFunction;
import java.util.stream.Stream;

public final class SignUtils {

    private static final Logger log = LoggerFactory.getLogger(SignUtils.class);

    private SignUtils() {
    }

    public static String makeSign(SignTypeEnum signTypeEnum, String signKey, Map<String, Object> params) {
        String raw = StringUtils.join(signKey, buildParamSign(params, "&"), signKey);
        String sign = signTypeEnum.getSignFunc().apply(signKey.getBytes(StandardCharsets.UTF_8), raw.getBytes());
        log.info("SignUtils.markSign. raw: {}, sign: {}", raw, sign);
        return sign;
    }

    private static String buildParamSign(Map<String, Object> params, String separatorChar) {
        Iterator<Map.Entry<String, Object>> iterator = new TreeMap<>(params).entrySet().iterator();
        StringBuilder builder = new StringBuilder();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            if (entry.getValue() != null) {
                builder.append(entry.getKey()).append("=").append(entry.getValue());
            }
            if (iterator.hasNext()) {
                builder.append(separatorChar);
            }
        }
        return builder.toString();
    }

    @Getter
    @ToString
    @AllArgsConstructor
    public enum SignTypeEnum {
        MD5(MessageDigestAlgorithms.MD5, (key, valueToDigest) -> DigestUtils.md5Hex(valueToDigest)),
        SHA_1(MessageDigestAlgorithms.SHA_1, (key, valueToDigest) -> DigestUtils.sha1Hex(valueToDigest)),
        SHA_256(MessageDigestAlgorithms.SHA_256, (key, valueToDigest) -> DigestUtils.sha256Hex(valueToDigest)),
        HMAC_SHA_256(HmacAlgorithms.HMAC_SHA_256.name(), SignTypeEnum::hmacSha256Hex)
        ;

        private final String algorithm;
        private final BiFunction<byte[], byte[], String> signFunc;

        public static Optional<SignTypeEnum> of(String signType) {
            return Stream.of(SignTypeEnum.values())
                    .filter(signTypeEnum -> signTypeEnum.getAlgorithm().equalsIgnoreCase(signType))
                    .findFirst();
        }

        public static SignTypeEnum ofChecked(String signType) {
            return of(signType).orElseThrow(() -> new ContextedRuntimeException("Illegal signType: " + signType));
        }

        public static String hmacSha256Hex(byte[] key, byte[] valueToDigest) {
            HmacUtils hmacUtils = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, key);
            return hmacUtils.hmacHex(valueToDigest);
        }

    }
}
