package org.ponderers.commons.util.experimental;

import lombok.SneakyThrows;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.reflect.MethodUtils;

import java.lang.reflect.Field;
import java.util.Arrays;

@SuppressWarnings("unchecked")
public final class ReflectionUtils {

    private ReflectionUtils() {
    }

    @SneakyThrows
    public static void writeStaticFinalField(final Class<?> cls, final String fieldName, final Object value) {
        int javaVersion = Arrays.stream(System.getProperty("java.version").split("\\."))
                .mapToInt(Integer::parseInt)
                .findFirst().orElse(0);
        System.out.println(javaVersion);
        Field field = FieldUtils.getField(cls, fieldName, true);
        FieldUtils.removeFinalModifier(field, true);
        FieldUtils.writeStaticField(field, value, true);
    }

    @SneakyThrows
    public static Object readStaticField(final Class<?> cls, final String fieldName) {
        Field field = FieldUtils.getField(cls, fieldName, true);
        return FieldUtils.readStaticField(field);
    }

    @SneakyThrows
    public static void writeStaticField(final Class<?> cls, final String fieldName, final Object value) {
        Field field = FieldUtils.getField(cls, fieldName, true);
        FieldUtils.writeStaticField(field, value, true);
    }

    @SneakyThrows
    public static void writeField(final Class<?> cls, final String fieldName, final Object target, final Object value) {
        Field field = FieldUtils.getField(cls, fieldName, true);
        FieldUtils.writeField(field, target, value, true);
    }

    @SneakyThrows
    public static <T> T invokeMethod(Object o, String methodName) {
        return (T) MethodUtils.invokeMethod(o, true, methodName);
    }

    @SneakyThrows
    public static <T> T invokeMethod(Object o, String methodName, Object... args) {
        return (T) MethodUtils.invokeMethod(o, true, methodName, args);
    }

}
