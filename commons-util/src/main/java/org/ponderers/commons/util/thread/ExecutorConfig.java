package org.ponderers.commons.util.thread;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutorConfig {

    private static final Logger log = LoggerFactory.getLogger(ExecutorConfig.class);

    public static final RejectedExecutionHandler ABORT_POLICY = new ThreadPoolExecutor.AbortPolicy();
    public static final RejectedExecutionHandler CALLER_RUNS_POLICY = new ThreadPoolExecutor.CallerRunsPolicy();
    public static final RejectedExecutionHandler DISCARD_OLDEST_POLICY = new ThreadPoolExecutor.DiscardOldestPolicy();
    public static final RejectedExecutionHandler DISCARD_POLICY = new ThreadPoolExecutor.DiscardPolicy();
    public static final RejectedExecutionHandler DISCARD_AND_LOG_POLICY = new ExecutorConfig.DiscardAndLogPolicy();

    protected int corePoolSize;
    protected int maximumPoolSize;
    protected long keepAliveTime;
    protected TimeUnit unit;
    protected int queueCapacity;
    protected String threadNamePrefix;
    protected RejectedExecutionHandler handler;

    public static ExecutorConfig custom(String threadNamePrefix) {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        int maximumPoolSize = availableProcessors * 2 + 1;
        return ExecutorConfig.builder()
                .corePoolSize(availableProcessors)
                .maximumPoolSize(maximumPoolSize)
                .keepAliveTime(60L)
                .unit(TimeUnit.SECONDS)
                .queueCapacity(1000)
                .threadNamePrefix(threadNamePrefix)
                .handler(ABORT_POLICY)
                .build();
    }

    public static class DiscardAndLogPolicy implements RejectedExecutionHandler {

        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.warn("task: {}, executor: {}", r, executor);
        }
    }
}
