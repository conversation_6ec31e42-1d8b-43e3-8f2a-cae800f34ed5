package org.ponderers.commons.util.experimental;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Properties;

public class ResourceUtils {

    private ResourceUtils() {
    }

    @SneakyThrows
    public static String resourceToString(String name) {
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        return IOUtils.resourceToString(name, StandardCharsets.UTF_8, contextClassLoader);
    }

    @SneakyThrows
    public static URL resourceToURL(String name) {
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        return IOUtils.resourceToURL(name, contextClassLoader);
    }

    @SneakyThrows
    public static Properties resourceToProperties(String name) {
        Properties properties = new Properties();
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        try (InputStream is = contextClassLoader.getResourceAsStream(name)) {
            if (Objects.nonNull(is)) properties.load(is);
        }
        return properties;
    }
}
