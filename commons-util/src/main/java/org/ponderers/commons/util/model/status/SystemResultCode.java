package org.ponderers.commons.util.model.status;

import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Stream;

@Getter
@ToString
public enum SystemResultCode implements ResultCode {

    SUCCESS(0, "操作成功"),
    FAILURE(1, "系统繁忙，请稍后重试"),
    E_10000(10000, "请求参数不合法"),
    E_10001(10001, "您的帐号因违规被封号，如有疑问请联系客服"),
    E_10002(10002, "因服务升级, 该支付渠道暂停服务"),
    E_10003(10003, "青少年模式开启中，不能充值和消费"),
    E_10004(10004, "功能暂时关闭，如有疑问请联系客服"),
    ;

    private final int code;
    private final String desc;

    SystemResultCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean isSuccess() {
        return SUCCESS.equals(this);
    }

    public static SystemResultCode codeOf(int code) {
        return Stream.of(SystemResultCode.values())
                .filter(sysResultCode -> sysResultCode.getCode() == code)
                .findFirst()
                .orElse(SystemResultCode.FAILURE);
    }

    public static String descOf(int code) {
        Optional<SystemResultCode> optionalSystemResultCode = Arrays.stream(SystemResultCode.values())
                .filter(systemResultCode -> systemResultCode.getCode() == code)
                .findAny();
        return optionalSystemResultCode.isPresent() ? optionalSystemResultCode.get().getDesc() : "";
    }

    public void postHandler(Consumer<ResultCode> onSuccess, Consumer<ResultCode> onFailure) {
        if (isSuccess()) {
            onSuccess.accept(this);
        } else {
            onFailure.accept(this);
        }
    }

}
