package org.ponderers.commons.util.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * Include raw JSON inside a Java object when json content deserialized using Jackson.
 * Use {@link JsonUtils#parseObject(String, Class)} for deserialized.
 * Using annotation <code>&#64;JsonDeserialize(using = KeepAsJsonDeserializer.class)</code> by specify the Deserializer class to use for deserializing associated value.
 *
 * <AUTHOR>
 * @see <a href="https://stackoverflow.com/questions/4783421/how-can-i-include-raw-json-in-an-object-using-jackson">https://stackoverflow.com/questions/4783421/how-can-i-include-raw-json-in-an-object-using-jackson</a>
 */
public class KeepAsJsonDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        TreeNode treeNode = p.getCodec().readTree(p);
        return treeNode.toString();
    }
}
