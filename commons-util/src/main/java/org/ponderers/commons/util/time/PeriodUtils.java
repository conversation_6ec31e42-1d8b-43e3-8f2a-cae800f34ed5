package org.ponderers.commons.util.time;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

public class PeriodUtils {

    private PeriodUtils() {
    }

    public static long getCurrentPeriod(PeriodType periodType) {
        return getPeriod(periodType, System.currentTimeMillis());
    }

    public static long getPeriod(PeriodType periodType, Date date) {
        return getPeriod(periodType, date.getTime());
    }

    public static long getPeriod(PeriodType periodType, long mills) {
        if (PeriodType.SECOND == periodType) {
            String periodStr = DateFormatUtils.format(mills, "yyyyMMddHHmmss");
            return Long.parseLong(periodStr);
        }
        if (PeriodType.MINUTE == periodType) {
            String periodStr = DateFormatUtils.format(mills, "yyyyMMddHHmm");
            return Long.parseLong(periodStr);
        }
        if (PeriodType.HOUR == periodType) {
            String periodStr = DateFormatUtils.format(mills, "yyyyMMddHH");
            return Long.parseLong(periodStr);
        }
        if (PeriodType.DAY == periodType) {
            String periodStr = DateFormatUtils.format(mills, "yyyyMMdd");
            return Long.parseLong(periodStr);
        }
        if (PeriodType.WEEK == periodType) {
            Date date = new Date(mills);
            int weekBasedYear = DateHelper.getWeekBasedYear(date);
            int week = DateHelper.getWeekOfYear(date);
            // 使用周一所在年份确保周标识符的唯一性
            String periodStr = weekBasedYear + String.format("%02d", week);
            return Long.parseLong(periodStr);
        }
        if (PeriodType.MONTH == periodType) {
            String periodStr = DateFormatUtils.format(mills, "yyyyMM");
            return Long.parseLong(periodStr);
        }
        if (PeriodType.QUARTER == periodType) {
            int quarter = DateHelper.getQuarterOfYear(mills);
            String periodStr = DateFormatUtils.format(mills, "yyyy") + quarter;
            return Long.parseLong(periodStr);
        }
        if (PeriodType.YEAR == periodType) {
            String periodStr = DateFormatUtils.format(mills, "yyyy");
            return Long.parseLong(periodStr);
        }
        if (PeriodType.EVER == periodType) {
            return 0L;
        }
        throw new ContextedRuntimeException("不支持的[periodType]类型")
                .addContextValue("periodType", periodType);
    }

    @Getter
    @ToString
    @AllArgsConstructor
    public enum PeriodType {
        SECOND(1, "每秒"),
        MINUTE(2, "每分"),
        HOUR(3, "每时"),
        DAY(4, "每日"),
        WEEK(5, "每周"),
        MONTH(6, "每月"),
        QUARTER(7, "每季"),
        YEAR(8, "每年"),
        EVER(9, "永久"),
        ;

        private final int code;
        private final String desc;

        public static Optional<PeriodType> of(int code) {
            return Arrays.stream(PeriodType.values())
                    .filter(periodType -> periodType.getCode() == code)
                    .findFirst();
        }
    }
}
