package org.ponderers.commons.util.experimental;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.IntConsumer;
import java.util.function.IntFunction;
import java.util.function.Predicate;

@SuppressWarnings("UnstableApiUsage")
public class LoopInvokerUtils {

    private static final Logger log = LoggerFactory.getLogger(LoopInvokerUtils.class);

    private LoopInvokerUtils() {
    }

    public static <R> List<R> invoke(double permitsPerSecond, int maxLoop, IntFunction<R> function, Predicate<R> hasMoreChecker) {
        RateLimiter rateLimiter = RateLimiter.create(permitsPerSecond);
        log.warn("loopInvoke function. rateLimiter: {}", rateLimiter);
        List<R> resultList = Lists.newArrayListWithCapacity(maxLoop);
        int times = 1;
        while (times <= maxLoop) {
            rateLimiter.acquire();
            R result = function.apply(times);
            resultList.add(result);
            if (!hasMoreChecker.test(result)) {
                log.warn("没有更多需要处理，退出循环调用。rateLimiter: {}, times: {}, result: {}",
                        rateLimiter, times, result);
                break;
            }
            times++;
        }
        return resultList;
    }

    public static void invoke(double permitsPerSecond, int maxLoop, IntConsumer consumer) {
        RateLimiter rateLimiter = RateLimiter.create(permitsPerSecond);
        log.warn("loopInvoke consumer. rateLimiter: {}", rateLimiter);
        int times = 1;
        while (times <= maxLoop) {
            rateLimiter.acquire(1);
            consumer.accept(times);
            times++;
        }
    }
}
