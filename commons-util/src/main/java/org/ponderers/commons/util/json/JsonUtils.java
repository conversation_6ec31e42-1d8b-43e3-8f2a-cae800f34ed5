package org.ponderers.commons.util.json;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.google.gson.JsonObject;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.TypeRef;
import com.jayway.jsonpath.spi.json.GsonJsonProvider;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.GsonMappingProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import lombok.SneakyThrows;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * JSON解析工具类
 *
 * <AUTHOR>
 */
public class JsonUtils {

    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);

    private JsonUtils() {
    }

    private static final ObjectMapper objectMapper = new ObjectMapper()
            .setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE)
            .setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY)
            .configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, false)
            .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true)
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
            .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)
            .configure(JsonGenerator.Feature.IGNORE_UNKNOWN, true)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    /**
     * Note that the JacksonJsonProvider requires com.fasterxml.jackson.core:jackson-databind:2.4.5 on your classpath.
     */
    public static final Configuration jsonPathJacksonConf = Configuration.builder()
            .options(Option.DEFAULT_PATH_LEAF_TO_NULL, Option.SUPPRESS_EXCEPTIONS)
            .jsonProvider(new JacksonJsonProvider())
            .mappingProvider(new JacksonMappingProvider(objectMapper))
            .build();

    /**
     * Note that the GsonJsonProvider requires com.google.code.gson:gson:2.3.1 on your classpath.
     */
    public static final Configuration jsonPathGsonConf = Configuration.builder()
            .options(Option.DEFAULT_PATH_LEAF_TO_NULL, Option.SUPPRESS_EXCEPTIONS)
            .jsonProvider(new GsonJsonProvider())
            .mappingProvider(new GsonMappingProvider())
            .build();

    public static <T> T parseJsonPath(String content, String path, Class<T> clazz, T defaultValue) {
        try {
            return parseJsonPath(content, path, clazz).orElse(defaultValue);
        } catch (Exception e) {
            log.error("[JsonUtils] parseJsonPath error. content: {}, path: {}, clazz: {}, defaultValue: {}",
                    content, path, clazz, defaultValue, e);
        }
        return defaultValue;
    }

    public static <T> T parseJsonPathChecked(String content, String path, Class<T> clazz) {
        return parseJsonPath(content, path, clazz).orElseThrow(() -> new ContextedRuntimeException("Invalid Json")
                .addContextValue("content", content)
                .addContextValue("path", path)
                .addContextValue("class", clazz.getCanonicalName()));
    }

    public static <T> Optional<T> parseJsonPath(String content, String path, Class<T> clazz) {
        T value = JsonPath.using(jsonPathJacksonConf).parse(content).read(path, clazz);
        return Optional.ofNullable(value);
    }

    public static <T> T parseJsonPathToObject(String content, String path, Class<T> clazz) {
        return JsonPath.using(jsonPathJacksonConf).parse(content).read(path, clazz);
    }

    public static <T> T parseJsonPathToTypeRef(String content, String path, TypeRef<T> typeRef) {
        return JsonPath.using(jsonPathJacksonConf).parse(content).read(path, typeRef);
    }

    public static JsonObject parseJsonPathGsonObject(String content, String path) {
        return JsonPath.using(jsonPathGsonConf).parse(content).read(path);
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    @SneakyThrows
    public static String xml2json(String xml) {
        XmlMapper xmlMapper = new XmlMapper();
        JsonNode jsonNode = xmlMapper.readTree(xml);
        return jsonNode.toString();
    }

    @SneakyThrows
    public static JsonNode parseJsonNode(String content) {
        return objectMapper.readTree(content);
    }

    @SneakyThrows
    public static <T> List<T> parseList(String content, Class<T> clazz) {
        CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        return objectMapper.readValue(content, collectionType);
    }

    @SneakyThrows
    public static <K, V> Map<K, V> parseMap(String content, Class<K> keyClass, Class<V> valueClass) {
        MapType mapType = objectMapper.getTypeFactory().constructMapType(HashMap.class, keyClass, valueClass);
        return objectMapper.readValue(content, mapType);
    }

    @SneakyThrows
    public static String toPrettyJSONString(Object value) {
        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(value);
    }

    /**
     * Compatible Fastjson
     * JSON.toJSONString(Object object)
     */
    @SneakyThrows
    public static String toJSONString(Object value) {
        return objectMapper.writeValueAsString(value);
    }

    /**
     * Compatible Fastjson
     * JSON.parseObject(String text, Class<T> clazz)
     */
    @SneakyThrows
    public static <T> T parseObject(String content, Class<T> clazz) {
        return objectMapper.readValue(content, clazz);
    }

    /**
     * Compatible Fastjson
     * JSON.parseObject(String str, TypeReference typeReference, Feature... features)
     */
    @SneakyThrows
    public static <T> T parseObject(String content, TypeReference<T> typeRef) {
        return objectMapper.readValue(content, typeRef);
    }

    /**
     * Compatible Fastjson
     * JSON.parseArray(String str, Feature... features)
     */
    @SneakyThrows
    public static <T> List<T> parseArray(String content, Class<T> clazz) {
        return parseList(content, clazz);
    }

    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        return objectMapper.convertValue(fromValue, toValueType);
    }

    public static boolean isValidJson(String content) {
        try {
            objectMapper.readTree(content);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
