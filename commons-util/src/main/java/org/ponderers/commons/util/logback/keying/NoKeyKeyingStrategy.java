package org.ponderers.commons.util.logback.keying;

/**
 * Evenly distributes all written log messages over all available kafka partitions.
 * This strategy can lead to unexpected read orders on clients.
 * <p>
 * https://github.com/danielwegener/logback-kafka-appender
 *
 * @since 0.0.1
 */
public class NoKeyKeyingStrategy implements KeyingStrategy<Object> {
    @Override
    public byte[] createKey(Object e) {
        return null;
    }
}
