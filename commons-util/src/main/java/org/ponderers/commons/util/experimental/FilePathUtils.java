package org.ponderers.commons.util.experimental;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

public final class FilePathUtils {

    private FilePathUtils() {
    }

    public static File file(String path) {
        if (null == path) {
            return null;
        }
        Path p = resolveRelativePath(path);
        return new File(p.toString());
    }

    public static Path resolveRelativePath(String relativePath) {
        // 处理 "~" 符号
        if (relativePath.startsWith("~")) {
            relativePath = System.getProperty("user.home") + relativePath.substring(1);
        }
        // 使用 Paths 类来解析和规范化路径
        return Paths.get(relativePath).normalize().toAbsolutePath();
    }
}
