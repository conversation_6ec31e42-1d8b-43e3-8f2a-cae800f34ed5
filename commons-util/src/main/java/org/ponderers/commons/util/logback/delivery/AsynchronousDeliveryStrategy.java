package org.ponderers.commons.util.logback.delivery;

import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;

/**
 * Dispatches each log message to the Kafka Producer. If the delivery fails for some reasons, the message is dispatched to the fallback appenders.
 * However, this DeliveryStrategy does block if the producers send buffer is full (this can happen if the connection to the broker gets lost).
 * To avoid even this blocking, enable the producerConfig block.on.buffer.full=false. All log messages that cannot be delivered fast enough will
 * then immediately go to the fallback appenders.
 */
public class AsynchronousDeliveryStrategy implements DeliveryStrategy {
    @Override
    public <K, V, E> boolean send(Producer<K, V> producer, ProducerRecord<K, V> producerRecord, final E event, final FailedDeliveryCallback<E> failedDeliveryCallback) {
        try {
            producer.send(producerRecord, (metadata, exception) -> {
                if (exception != null) {
                    failedDeliveryCallback.onFailedDelivery(event, exception);
                }
            });
            return true;
        } catch (Exception e) {
            failedDeliveryCallback.onFailedDelivery(event, e);
            return false;
        }
    }
}
