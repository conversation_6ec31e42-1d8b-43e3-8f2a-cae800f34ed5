package org.ponderers.commons.util.http;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HttpRequest {

    private String url;
    @Builder.Default
    private Map<String, String> headers = Maps.newHashMap();
    @Builder.Default
    private Map<String, String> queryParameters = Maps.newHashMap();
    @Builder.Default
    private Map<String, String> bodyParams = Maps.newHashMap();
    private String jsonBody;

    public static HttpRequest.HttpRequestBuilder custom() {
        return HttpRequest.builder();
    }
}
