package org.ponderers.commons.util.experimental;

import java.math.BigDecimal;
import java.math.RoundingMode;

public final class BigDecimalUtils {

    private BigDecimalUtils() {
    }

    public static boolean isPositive(BigDecimal value) {
        return value.compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isNegative(BigDecimal value) {
        return value.compareTo(BigDecimal.ZERO) < 0;
    }

    public static boolean isZero(BigDecimal value) {
        return value.compareTo(BigDecimal.ZERO) == 0;
    }

    public static BigDecimal multiply(BigDecimal value, BigDecimal multiplicand) {
        return value.multiply(multiplicand);
    }

    public static BigDecimal divide(BigDecimal value, BigDecimal divisor) {
        return value.divide(divisor, 2, RoundingMode.HALF_UP);
    }

}
