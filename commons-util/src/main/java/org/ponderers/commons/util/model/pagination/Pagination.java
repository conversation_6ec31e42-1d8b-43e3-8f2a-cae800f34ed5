package org.ponderers.commons.util.model.pagination;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Pagination Model
 *
 * <AUTHOR>
 */
@ToString
@Getter(value = AccessLevel.PUBLIC)
@Setter(value = AccessLevel.PRIVATE)
public class Pagination {

    private int currPage;   // 当前页
    private int pageSize;   // 页面大小
    private int totalCount; // 总记录数
    private int totalPage;  // 总页数
    private int firstPage;  // 第一页
    private int prevPage;   // 上一页
    private int nextPage;   // 下一页
    private int lastPage;   // 最后一页
    private int offset;     // SQL偏移量
    private int limit;      // SQL限制数

    private Pagination() {
    }

    public static Pagination.Builder builder(int currPage, int pageSize) {
        return new Pagination.Builder(currPage, pageSize);
    }

    /**
     * Calculate Pagination Information
     *
     * @param totalCount total row count
     */
    public void calculatePagination(int totalCount) {
        this.setTotalPage(calculateTotalPage(totalCount));
        this.setFirstPage(1);
        this.setLastPage(calculateTotalPage(totalCount));
        this.setPrevPage(Math.max(this.currPage - 1, 1));
        this.setNextPage(Math.min(this.totalPage, this.currPage + 1));
        this.setTotalCount(totalCount);
    }

    private int calculateTotalPage(int totalCount) {
        return Math.max(totalCount / Math.max(pageSize, 1), 1);
    }

    public static final class Builder {

        private final Pagination pagination;

        public Builder(int currPage, int pageSize) {
            this.pagination = new Pagination();
            this.pagination.setCurrPage(Math.max(currPage, 1));
            this.pagination.setPageSize(Math.max(pageSize, 1));
            this.pagination.setOffset(Math.max(currPage - 1, 0) * Math.max(pageSize, 1));
            this.pagination.setLimit(Math.max(pageSize, 1));
        }

        public Pagination build() {
            return this.pagination;
        }

    }

}
