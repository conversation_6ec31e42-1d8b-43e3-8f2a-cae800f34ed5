package org.ponderers.commons.util.probability;

import lombok.extern.slf4j.Slf4j;
import org.ponderers.commons.util.json.JsonUtils;

import java.util.List;

@Slf4j
public class ProbabilityUtils {

    private ProbabilityUtils() {

    }

    public static List<ProbabilityEvent> generateProbabilityEvents(String json, int input) {
        List<ProbabilityEvent> probabilityEvents = JsonUtils.parseList(json, ProbabilityEvent.class);
        probabilityEvents.forEach(probabilityEvent -> probabilityEvent.calculateProbability(input));
        return probabilityEvents;
    }
}
