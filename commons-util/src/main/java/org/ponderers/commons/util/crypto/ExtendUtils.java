package org.ponderers.commons.util.crypto;

import com.jayway.jsonpath.TypeRef;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.binary.Base64;
import org.ponderers.commons.util.json.JsonUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * {
 * "callbackSign" : {
 * "signType" : "MD5",
 * "sign" : "ed37b117b76fca1ccb63568420689095"
 * },
 * "callbackArgs" : {
 * "amount" : "0.02",
 * "addTime" : "1650802549",
 * "version" : "20170111"
 * }
 * }
 */
public class ExtendUtils {

    private ExtendUtils() {
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Extend {

        private CallbackSign callbackSign;
        private Map<String, Object> callbackArgs;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class CallbackSign {
            /**
             * 签名算法类型
             */
            private String signType;
            /**
             * 参数的签名串
             */
            private String sign;
        }
    }

    public static String createExtend(SignUtils.SignTypeEnum signTypeEnum, String signKey, Map<String, Object> callbackArgs) {
        Extend.CallbackSign callbackSign = Extend.CallbackSign.builder()
                .signType(signTypeEnum.getAlgorithm())
                .sign(SignUtils.makeSign(signTypeEnum, signKey, callbackArgs))
                .build();
        ExtendUtils.Extend extend = new ExtendUtils.Extend();
        extend.setCallbackSign(callbackSign);
        extend.setCallbackArgs(callbackArgs);
        return encodeExtend(JsonUtils.toJSONString(extend));
    }

    public static boolean checkExtend(String extend, String signKey) {
        Extend.CallbackSign callbackSign = getCallbackSign(extend);
        SignUtils.SignTypeEnum signTypeEnum = SignUtils.SignTypeEnum.ofChecked(callbackSign.getSignType());
        Map<String, Object> params = getCallbackArgs(extend);
        String expectSign = SignUtils.makeSign(signTypeEnum, signKey, params);
        String actualSign = callbackSign.getSign();
        return expectSign.equals(actualSign);
    }

    public static String decodeExtend(String extend) {
        return new String(Base64.decodeBase64(extend), StandardCharsets.UTF_8);
    }

    public static String encodeExtend(String decodedExtend) {
        return new String(Base64.encodeBase64(decodedExtend.getBytes(StandardCharsets.UTF_8)));
    }

    public static <T> T getCallbackArgsByName(String extend, String argName, Class<T> clazz, T defaultValue) {
        return JsonUtils.parseJsonPath(decodeExtend(extend), "$.callbackArgs." + argName, clazz, defaultValue);
    }

    public static Map<String, Object> getCallbackArgs(String extend) {
        TypeRef<Map<String, Object>> typeRef = new TypeRef<>() {
        };
        return JsonUtils.parseJsonPathToTypeRef(decodeExtend(extend), "$.callbackArgs", typeRef);
    }

    public static Extend.CallbackSign getCallbackSign(String extend) {
        return JsonUtils.parseJsonPathChecked(decodeExtend(extend), "$.callbackSign", Extend.CallbackSign.class);
    }

    public static Extend parseExtend(String extendStr) {
        String json = decodeExtend(extendStr);
        return JsonUtils.parseObject(json, Extend.class);
    }
}
