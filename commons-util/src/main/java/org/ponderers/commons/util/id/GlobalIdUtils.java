package org.ponderers.commons.util.id;

import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class GlobalIdUtils {

    private GlobalIdUtils() {
    }

    /**
     * 订单号
     * [YmdHis(14) + 时间戳(6位) + UUID(19位)]
     * <p>
     * Since the UUID is 128 bits and the int is only 32bit.
     * You'll either have to accept the risk of collisions and try to fudge it to a smaller space,
     * hashCode() is probably a good way to do that.
     * Or find alternative, use the UUID directly, just mapping the UUID to a BigInteger
     *
     * @return orderNo
     */
    public static String newOrderId() {
        String dateInfo = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
        String nanoInfo = StringUtils.right(String.valueOf(System.nanoTime()), 6);
        String uuidInfo = Strings.padStart(String.valueOf(UUID.randomUUID().getMostSignificantBits() & Long.MAX_VALUE), 19, '0');
        return dateInfo + nanoInfo + uuidInfo;
    }

    /**
     * Generate UUID
     *
     * @return orderNo
     */
    public static String newUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * Generate Snowflake Id
     *
     * @return orderNo
     */
    public static long newSnowflakeId(final SnowflakeIdGenerator snowflakeIdGenerator) {
        return snowflakeIdGenerator.nextId();
    }

    /**
     * Generate Concurrent Id
     *
     * @return orderNo
     */
    public static long newConcurrentId(final ConcurrentIdGenerator concurrentIdGenerator) {
        return concurrentIdGenerator.nextId();
    }

    /**
     * <pre>
     * 订单前缀：R，长度：1
     * 订单机房：0000，长度：4
     * 订单时间：20200404，长度：8
     * 订单编号：9223372036854775807，长度：19（注意：使用Snowflake ID算法生成）
     * 订单示例：R0000202004049223372036854775807，长度：32
     *
     * region：可以简单理解为地理上的分区，比如亚洲地区，或者华北地区，再或者北京等等，没有具体大小的限制。
     * zone：可以简单理解为region内的具体机房，比如说region划分为北京，然后北京有两个机房，就可以在此region之下划分出zone1,zone2两个zone。
     * Snowflake ID：生成的数值最大长度为Long.MAX_VALUE（其最高位固定为0），固其长度为19
     * </pre>
     */
    public static String newOrderNo(final SnowflakeIdGenerator snowflakeIdGenerator) {
        return newOrderNo(newSnowflakeId(snowflakeIdGenerator));
    }

    public static Optional<String> parseYearMonth(String orderNo) {
        final String regex = "^\\w(\\d{4})(\\d{6})";
        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(orderNo);
        if (matcher.find()) {
            return Optional.of(matcher.group(2));
        }
        return Optional.empty();
    }

    /**
     * <pre>
     * 订单前缀：T，长度：1
     * 订单机房：0000，长度：4
     * 订单时间：20200404，长度：8
     * 订单编号：9223372036854775807，长度：19（注意：使用Snowflake ID算法生成）
     * 订单示例：T0000202004049223372036854775807，长度：32
     *
     * region：可以简单理解为地理上的分区，比如亚洲地区，或者华北地区，再或者北京等等，没有具体大小的限制。
     * zone：可以简单理解为region内的具体机房，比如说region划分为北京，然后北京有两个机房，就可以在此region之下划分出zone1,zone2两个zone。
     * Snowflake ID：生成的数值最大长度为Long.MAX_VALUE（其最高位固定为0），固其长度为19
     * </pre>
     */
    public static String newOrderNo(final long snowflakeId) {
        String regionZone = "0000";
        String yearMonth = DateFormatUtils.format(new Date(), "yyyyMMdd");
        return String.format("T%s%s%s", regionZone, yearMonth, snowflakeId);
    }

    public static String newNanoId() {
        return NanoIdGenerator.randomNanoId();
    }

}
