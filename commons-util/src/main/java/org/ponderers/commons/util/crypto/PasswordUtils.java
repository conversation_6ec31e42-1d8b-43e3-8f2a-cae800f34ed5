package org.ponderers.commons.util.crypto;

import com.google.common.primitives.Bytes;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;

/**
 * Password Utils
 *
 * <AUTHOR>
 */
public class PasswordUtils {

    private PasswordUtils() {}

    /**
     * Returns a random salt to be used to hash a password.
     *
     * @return a 20 bytes random salt
     */
    public static byte[] getNextSalt() {
        return RandomUtils.nextBytes(20);
    }

    /**
     * Returns true if the given password and salt match the hashed value, false otherwise.<br>
     * Note - side effect: the password is destroyed (the char[] is filled with zeros)
     *
     * @param password     the password to check
     * @param salt         the salt used to hash the password
     * @param expectedHash the expected hashed value of the password
     * @return true if the given password and salt match the hashed value, false otherwise
     */
    public static boolean isValidPassword(byte[] password, byte[] salt, byte[] expectedHash) {
        byte[] hash = hash(password, salt);
        if (hash.length != expectedHash.length) return false;
        for (int i = 0; i < hash.length; i++) {
            if (hash[i] != expectedHash[i]) return false;
        }
        return true;
    }

    /**
     * Returns a salted and hashed password using the provided hash.<br>
     * Note - side effect: the password is destroyed (the char[] is filled with zeros)
     *
     * @param password the password to be hashed
     * @param salt     a 16 bytes salt, ideally obtained with the getNextSalt method
     * @return the hashed password with a pinch of salt
     */
    public static byte[] hash(byte[] password, byte[] salt) {
        return DigestUtils.sha256(Bytes.concat(password, salt));
    }

    /**
     * Generates a random password of a given length, using letters and digits.
     *
     * @param length the length of the password
     * @return a random password
     */
    public static String generateRandomPassword(int length) {
        return RandomStringUtils.random(length);
    }

}
