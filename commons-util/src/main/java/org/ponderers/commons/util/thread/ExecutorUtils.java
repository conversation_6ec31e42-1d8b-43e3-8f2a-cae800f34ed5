package org.ponderers.commons.util.thread;

import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;

import java.util.Objects;
import java.util.concurrent.*;

public class ExecutorUtils {

    private ExecutorUtils() {
    }

    public static ExecutorService createPool(String threadNamePrefix) {
        return createPool(threadNamePrefix, null);
    }

    public static ExecutorService createPool(String threadNamePrefix, MeterRegistry meterRegistry) {
        ExecutorConfig executorConfig = ExecutorConfig.custom(threadNamePrefix);
        return createPool(executorConfig, meterRegistry);
    }

    public static ExecutorService createPool(ExecutorConfig executorConfig) {
        return createPool(executorConfig, null);
    }

    public static ExecutorService createPool(ExecutorConfig executorConfig, MeterRegistry meterRegistry) {
        String threadNamePrefix = executorConfig.getThreadNamePrefix();
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(threadNamePrefix + "-%d").build();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(executorConfig.getCorePoolSize(),
                executorConfig.getMaximumPoolSize(), executorConfig.getKeepAliveTime(),
                executorConfig.getUnit(), new LinkedBlockingQueue<>(executorConfig.getQueueCapacity()), threadFactory, executorConfig.getHandler());
        if (Objects.nonNull(meterRegistry)) {
            String executorServiceName = threadNamePrefix + "-monitor";
            return ExecutorServiceMetrics.monitor(meterRegistry, threadPoolExecutor, executorServiceName);
        }
        return MoreExecutors.getExitingExecutorService(threadPoolExecutor, 120, TimeUnit.SECONDS);
    }

    public static ExecutorService createScheduledPool(String threadNamePrefix, Runnable command,
                                                      long initialDelay, long delay, TimeUnit unit) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadNamePrefix + "-%d")
                .setDaemon(true)
                .build();
        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor(threadFactory);
        executorService.scheduleWithFixedDelay(command, initialDelay, delay, unit);
        return executorService;
    }

}
