package org.ponderers.commons.util.logback.converter;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import org.ponderers.commons.util.net.IpUtils;

/**
 * Logback converter for %ip
 * <pre>{@code
 * <conversionRule conversionWord="ip" converterClass="org.ponderers.commons.util.logback.converter.IPConverter" />
 * }</pre>
 *
 * <AUTHOR>
 */
public class IPConverter extends ClassicConverter {

    private static final String serverIpAddress;

    static {
        serverIpAddress = IpUtils.getServerIpAddress();
    }

    @Override
    public String convert(ILoggingEvent event) {
        return serverIpAddress;
    }
}

