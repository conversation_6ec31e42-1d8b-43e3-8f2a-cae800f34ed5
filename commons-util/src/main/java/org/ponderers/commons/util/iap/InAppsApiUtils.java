package org.ponderers.commons.util.iap;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.time.DateUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jetbrains.annotations.NotNull;
import org.ponderers.commons.util.crypto.PemUtils;
import org.ponderers.commons.util.http.HttpRequest;
import org.ponderers.commons.util.http.OkHttpUtils;
import org.ponderers.commons.util.json.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.security.cert.*;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.ECPublicKey;
import java.util.*;
import java.util.stream.Collectors;


public class InAppsApiUtils {

    private static final Logger log = LoggerFactory.getLogger(InAppsApiUtils.class);

    private static final String PRODUCT_HOST = "https://api.storekit.itunes.apple.com";
    private static final String SANDBOX_HOST = "https://api.storekit-sandbox.itunes.apple.com";

    private InAppsApiUtils() {
    }

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    public static Map<String, String> httpHeaders(String token) {
        return Map.of("Authorization", "Bearer " + token);
    }

    public static String httpURL(String uri, boolean isSandbox) {
        return (isSandbox ? SANDBOX_HOST : PRODUCT_HOST) + uri;
    }

    /**
     * <a href="https://developer.apple.com/documentation/appstoreserverapi/get_transaction_info">https://developer.apple.com/documentation/appstoreserverapi/get_transaction_info</a>
     */
    public static Optional<TransactionInfo> inAppsV1Transactions(final OkHttpClient client, String token,
                                                                 String transactionId, boolean isSandbox) {
        String url = httpURL("/inApps/v1/transactions/", isSandbox) + transactionId;
        HttpRequest request = HttpRequest.builder().url(url).headers(httpHeaders(token)).build();
        Optional<String> optionalJson = OkHttpUtils.syncGet(client, request);
        Optional<TransactionInfo> optionalTransaction = optionalJson.map(json -> {
            String signedTransactionInfo = JsonUtils.parseJsonPathChecked(json, "$.signedTransactionInfo", String.class);
            return parseSignedTransactionInfo(signedTransactionInfo);
        });
        log.warn("inAppsV1Transactions, transactionId:{}, optionalJson: {}, optionalTransaction: {}",
                transactionId, optionalJson, optionalTransaction);
        return optionalTransaction;
    }

    /**
     * <a href="https://developer.apple.com/documentation/appstoreserverapi/get_transaction_history">https://developer.apple.com/documentation/appstoreserverapi/get_transaction_history</a>
     * {"revision":"0","bundleId":"com.kugou.fanxingappstore","appAppleId":774384491,"environment":"Production","hasMore":false,"signedTransactions":[]}
     */
    public static Optional<HistoryResponse> inAppsV1History(final OkHttpClient client, String token, String transactionId,
                                                            HistoryOptions historyOptions, boolean isSandbox) {
        String url = httpURL("/inApps/v1/history/", isSandbox) + transactionId;
        Map<String, String> queryParameters = Maps.newHashMap();
        queryParameters.put("sort", historyOptions.getSort().name());
        historyOptions.getProductType().ifPresent(productType -> queryParameters.put("productType", productType.name()));
        historyOptions.getRevoked().ifPresent(revoked -> queryParameters.put("revoked", revoked.toString()));
        historyOptions.getRevision().ifPresent(revision -> queryParameters.put("revision", revision));
        HttpRequest request = HttpRequest.builder().url(url)
                .headers(httpHeaders(token))
                .queryParameters(queryParameters)
                .build();
        Optional<String> optionalJson = OkHttpUtils.syncGet(client, request);
        log.warn("inAppsV1History, transactionId:{}, historyOptions: {}, optionalJson: {}",
                transactionId, historyOptions, optionalJson);
        return optionalJson.map(json -> JsonUtils.parseObject(json, HistoryResponse.class));
    }

    /**
     * <a href="https://developer.apple.com/documentation/appstoreserverapi/get_all_subscription_statuses">https://developer.apple.com/documentation/appstoreserverapi/get_all_subscription_statuses</a>
     */
    public static Optional<SubscriptionsResponse> inAppsV1Subscriptions(final OkHttpClient client, String token,
                                                                        String transactionId, boolean isSandbox) {
        String url = httpURL("/inApps/v1/subscriptions/", isSandbox) + transactionId;
        HttpRequest request = HttpRequest.builder().url(url).headers(httpHeaders(token)).build();
        Optional<String> optionalJson = OkHttpUtils.syncGet(client, request);
        log.warn("inAppsV1Subscriptions, transactionId: {}, optionalJson: {}", transactionId, optionalJson);
        return optionalJson.map(json -> JsonUtils.parseObject(json, SubscriptionsResponse.class));
    }

    /**
     * <a href="https://developer.apple.com/documentation/appstoreserverapi/get_refund_history">https://developer.apple.com/documentation/appstoreserverapi/get_refund_history</a>
     */
    public static Optional<RefundLookupResponse> inAppsV2RefundLookup(final OkHttpClient client, String token, String transactionId,
                                                        String revision, boolean isSandbox) {
        String url = httpURL("/inApps/v2/refund/lookup/", isSandbox) + transactionId;
        Map<String, String> queryParameters = Maps.newHashMap();
        if (StringUtils.isNotBlank(revision)) {
            queryParameters.put("revision", revision);
        }
        HttpRequest request = HttpRequest.builder()
                .url(url)
                .headers(httpHeaders(token))
                .queryParameters(queryParameters)
                .build();
        Optional<String> optionalJson = OkHttpUtils.syncGet(client, request);
        log.warn("inAppsV2RefundLookup. optionalJson: {}", optionalJson);
        return optionalJson.map(s -> JsonUtils.parseObject(s, RefundLookupResponse.class));
    }

    /**
     * <a href="https://developer.apple.com/documentation/appstoreserverapi/look_up_order_id">https://developer.apple.com/documentation/appstoreserverapi/look_up_order_id</a>
     */
    public static Optional<LookupResponse> inAppsV1Lookup(final OkHttpClient client, String token,
                                                  String orderId, boolean isSandbox) {
        String url = httpURL("/inApps/v1/lookup/", isSandbox) + orderId;
        HttpRequest request = HttpRequest.builder().url(url).headers(httpHeaders(token)).build();
        Optional<String> optionalJson = OkHttpUtils.syncGet(client, request);
        log.warn("inAppsV1Lookup, orderId: {}, optionalJson: {}", orderId, optionalJson);
        return optionalJson.map(s -> JsonUtils.parseObject(s, LookupResponse.class));
    }

    @NotNull
    public static RenewalInfo parseSignedRenewalInfo(String signedRenewalInfo) {
        DecodedJWT decodedJWT = validateSignedTransactionInfo(signedRenewalInfo);
        String payload = decodedJWT.getPayload();
        String renewalJson = new String(Base64.getDecoder().decode(payload), StandardCharsets.UTF_8);
        log.warn("renewalJson: {}", renewalJson);
        return JsonUtils.parseObject(renewalJson, RenewalInfo.class);
    }

    @NotNull
    public static TransactionInfo parseSignedTransactionInfo(String signedTransactionInfo) {
        DecodedJWT decodedJWT = validateSignedTransactionInfo(signedTransactionInfo);
        String payload = decodedJWT.getPayload();
        String transactionJson = new String(Base64.getDecoder().decode(payload), StandardCharsets.UTF_8);
        log.warn("transactionJson: {}", transactionJson);
        return JsonUtils.parseObject(transactionJson, TransactionInfo.class);
    }

    /**
     * <a href="https://developer.apple.com/documentation/appstoreserverapi/generating_tokens_for_api_requests">https://developer.apple.com/documentation/appstoreserverapi/generating_tokens_for_api_requests</a>
     *
     * @param bid        Your app’s bundle ID (Ex: “com.example.testbundleid2021”)
     * @param kid        Your private key ID from App Store Connect (Ex: 2X9R4HXF34)
     * @param iss        Your issuer ID from the Keys page in App Store Connect (Ex: "57246542-96fe-1a63-e053-0824d011072a")
     * @param p8filepath The SubscriptionKey_{kid}.p8 file contains the PRIVATE KEY that is used to SIGN the JWT content.
     * @return IAP JWT Token
     */
    @SneakyThrows
    public static String createIapJwtTokenByFilepath(String bid, String kid, String iss, String p8filepath) {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(p8filepath);
        Preconditions.checkNotNull(inputStream, "配置文件[%s]不存在", p8filepath);
        ECPrivateKey privateKey = (ECPrivateKey) PemUtils.readPrivateKeyFromInputStream(inputStream, "EC");
        return createIapJwtToken(bid, kid, iss, privateKey);
    }

    public static String createIapJwtTokenByContent(String bid, String kid, String iss, String p8content) {
        Preconditions.checkArgument(StringUtils.isNoneBlank(p8content), "p8content参数不允许为空");
        ECPrivateKey privateKey = (ECPrivateKey) PemUtils.loadPrivateKeyFromString(p8content, "EC", "BC");
        return createIapJwtToken(bid, kid, iss, privateKey);
    }

    private static String createIapJwtToken(String bid, String kid, String iss, ECPrivateKey privateKey) {
        Algorithm algorithm = Algorithm.ECDSA256(null, privateKey);
        Map<String, Object> headerClaims = Maps.newHashMap();
        headerClaims.put("alg", "ES256");
        headerClaims.put("kid", kid);
        headerClaims.put("typ", "JWT");
        // The time at which you issue the token, in UNIX time, in seconds (Ex: 1623085200)
        Date issuedAt = new Date();
        // The token’s expiration time, in UNIX time, in seconds.
        // Tokens that expire more than 60 minutes after the time in iat are not valid (Ex: 1623086400)
        Date expiresAt = DateUtils.addMinutes(issuedAt, 60);
        return JWT.create()
                .withHeader(headerClaims)
                .withIssuer(iss)
                .withIssuedAt(issuedAt)
                .withExpiresAt(expiresAt)
                .withAudience("appstoreconnect-v1")
                .withClaim("bid", bid)
                .sign(algorithm);
    }

    @SneakyThrows
    public static DecodedJWT validateSignedTransactionInfo(String signedTransactionInfo) {
        DecodedJWT decodedJWT = JWT.decode(signedTransactionInfo);
        String[] certs = decodedJWT.getHeaderClaim("x5c").asArray(String.class);
        // 苹果根证书
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        String appleRootCA = "AppleRootCA-G3.cer";
        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(appleRootCA);
        Preconditions.checkNotNull(is, "配置文件[%s]不存在", appleRootCA);
        Preconditions.checkArgument(is.available() > 0, "配置文件[%s]为空", appleRootCA);
        X509Certificate appleRootCertificate = (X509Certificate) certificateFactory.generateCertificate(is);
        // 解析证书链
        List<X509Certificate> x509Certificates = Arrays.stream(certs).map(cert -> {
            try (InputStream inputStream = new ByteArrayInputStream(Base64.getDecoder().decode(cert))) {
                return (X509Certificate) certificateFactory.generateCertificate(inputStream);
            } catch (Exception e) {
                throw new ContextedRuntimeException("解析证书链异常", e).addContextValue("cert", cert);
            }
        }).collect(Collectors.toList());

        // Ensure that there is a valid X.509 chain of trust from the signature to the root CA. Specifically,
        // ensure that the signature was created using the private key corresponding to the leaf certificate,
        // that the leaf certificate is signed by the intermediate CA, and that the intermediate CA is signed by the Apple Root CA - G3.
        verifyCertificate(x509Certificates.get(0), appleRootCertificate, x509Certificates);

        // 验证收据
        X509Certificate x509Certificate = x509Certificates.stream().findFirst()
                .orElseThrow(() -> new ContextedRuntimeException("无法读取 Apple Worldwide Developer Relations 证书"));
        Algorithm algorithm = Algorithm.ECDSA256((ECPublicKey) x509Certificate.getPublicKey());
        JWTVerifier verifier = JWT.require(algorithm).build();
        return verifier.verify(decodedJWT);
    }

    private static void verifyCertificate(X509Certificate leafCertificate, X509Certificate trustedRootCert,
                                          List<X509Certificate> intermediateCerts) {
        try {
            if (Security.getProvider("BC") == null) {
                Security.addProvider(new BouncyCastleProvider());
            }

            // Create the selector that specifies the starting certificate
            X509CertSelector selector = new X509CertSelector();
            selector.setCertificate(leafCertificate);

            // Create the trust anchors (set of root CA certificates)
            Set<TrustAnchor> trustAnchors = new HashSet<>();
            trustAnchors.add(new TrustAnchor(trustedRootCert, null));

            // Configure the PKIX certificate builder algorithm parameters
            PKIXBuilderParameters pkixParams = new PKIXBuilderParameters(trustAnchors, selector);

            // Disable CRL checks (this is done manually as additional step)
            pkixParams.setRevocationEnabled(false);

            // Specify a list of intermediate certificates
            CertStore intermediateCertStore = CertStore.getInstance("Collection", new CollectionCertStoreParameters(intermediateCerts), "BC");
            pkixParams.addCertStore(intermediateCertStore);

            // Build and verify the certification chain
            CertPathBuilder builder = CertPathBuilder.getInstance("PKIX", "BC");

            // If no exception thrown, it means the validation passed.
            PKIXCertPathBuilderResult pkixCertPathBuilderResult = (PKIXCertPathBuilderResult) builder.build(pkixParams);

            Objects.requireNonNull(pkixCertPathBuilderResult);
        } catch (Exception e) {
            throw new ContextedRuntimeException("Failed to validate chain of trust for apple certificates.", e);
        }
    }
}
