package org.ponderers.commons.util.iap;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;
import java.util.stream.Stream;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoryOptions {
    /**
     * An optional filter that indicates the product identifier to include in the transaction history.
     * Your query may specify more than one productID.
     */
    @Builder.Default
    private Optional<String> productId = Optional.empty();
    /**
     * An optional filter that indicates the product type to include in the transaction history. Your query may specify more than one productType.
     * Possible Values: AUTO_RENEWABLE, NON_RENEWABLE, CONSUMABLE, NON_CONSUMABLE
     */
    @Builder.Default
    private Optional<ProductType> productType = Optional.empty();
    /**
     * An optional sort order for the transaction history records. The response sorts the transaction records by their
     * recently modified date. The default value is ASCENDING, so you receive the oldest records first.
     * Possible Values: ASCENDING, DESCENDING
     */
    @Builder.Default
    private Sort sort = Sort.DESCENDING;
    /**
     * An optional filter that indicates the subscription group identifier to include in the transaction history.
     * Your query may specify more than one subscriptionGroupIdentifier.
     */
    @Builder.Default
    private Optional<String> subscriptionGroupIdentifier = Optional.empty();
    /**
     * An optional filter that limits the transaction history by the in-app ownership type.
     */
    @Builder.Default
    private Optional<String> inAppOwnershipType = Optional.empty();
    /**
     * An optional Boolean value that indicates whether the response includes only revoked transactions when the value
     * is true, or contains only nonrevoked transactions when the value is false. By default, the request doesn't include this parameter.
     * Possible Values: true, false
     */
    @Builder.Default
    private Optional<Boolean> revoked = Optional.empty();
    /**
     * A token you provide to get the next set of up to 20 transactions. All responses include a revision token. Use the revision token from the previous HistoryResponse.
     * Note: The revision token is required in all requests except the initial request. For requests that use the revision token, include the same query parameters from the initial request.
     */
    @Builder.Default
    private Optional<String> revision = Optional.empty();

    public enum Sort {
        ASCENDING, DESCENDING;

        public static Sort of(String sort) {
            return Stream.of(Sort.values())
                    .filter(item -> item.name().equalsIgnoreCase(sort))
                    .findFirst().orElse(Sort.ASCENDING);
        }
    }

    public enum ProductType {
        AUTO_RENEWABLE, NON_RENEWABLE, CONSUMABLE, NON_CONSUMABLE;

        public static Optional<ProductType> of(String productType) {
            return Stream.of(ProductType.values())
                    .filter(item -> item.name().equalsIgnoreCase(productType))
                    .findFirst();
        }
    }

}


