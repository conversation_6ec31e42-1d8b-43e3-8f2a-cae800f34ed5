package org.ponderers.commons.util.iap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionInfo {
    @JsonProperty("inAppOwnershipType")
    private String inAppOwnershipType;
    @JsonProperty("purchaseDate")
    private long purchaseDate;
    @JsonProperty("quantity")
    private int quantity;
    @JsonProperty("productId")
    private String productId;
    @JsonProperty("bundleId")
    private String bundleId;
    @JsonProperty("revocationDate")
    private long revocationDate;
    @JsonProperty("storefrontId")
    private String storefrontId;
    @JsonProperty("type")
    private String type;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("transactionReason")
    private String transactionReason;
    @JsonProperty("environment")
    private String environment;
    @JsonProperty("originalTransactionId")
    private String originalTransactionId;
    @JsonProperty("price")
    private int price;
    @JsonProperty("signedDate")
    private long signedDate;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("revocationReason")
    private int revocationReason;
    @JsonProperty("originalPurchaseDate")
    private long originalPurchaseDate;
    @JsonProperty("storefront")
    private String storefront;
}