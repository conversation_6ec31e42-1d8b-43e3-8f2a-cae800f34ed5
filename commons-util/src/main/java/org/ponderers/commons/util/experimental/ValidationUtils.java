package org.ponderers.commons.util.experimental;


import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.ValidatorFactory;

import java.util.Optional;
import java.util.Set;

public final class ValidationUtils {

    private ValidationUtils() {
    }

    public static <T> Optional<ConstraintViolation<T>> validate(T param) {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            Set<ConstraintViolation<T>> violations = factory.getValidator().validate(param);
            return violations.stream().findFirst();
        }
    }
}
