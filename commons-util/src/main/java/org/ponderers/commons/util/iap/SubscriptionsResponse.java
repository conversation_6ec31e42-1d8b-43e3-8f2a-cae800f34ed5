package org.ponderers.commons.util.iap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionsResponse {

    @JsonProperty("environment")
    private String environment;
    @JsonProperty("data")
    private List<DataItem> data;
    @JsonProperty("bundleId")
    private String bundleId;
    @JsonProperty("appAppleId")
    private int appAppleId;

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class DataItem {
		@JsonProperty("subscriptionGroupIdentifier")
		private String subscriptionGroupIdentifier;
		@JsonProperty("lastTransactions")
		private List<LastTransactionsItem> lastTransactions;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class LastTransactionsItem {
		@JsonProperty("originalTransactionId")
		private String originalTransactionId;
		@JsonProperty("signedTransactionInfo")
		private String signedTransactionInfo;
		@JsonProperty("signedRenewalInfo")
		private String signedRenewalInfo;
		@JsonProperty("status")
		private int status;
	}
}

