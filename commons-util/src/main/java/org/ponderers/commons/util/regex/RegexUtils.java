package org.ponderers.commons.util.regex;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * Regex Utils
 *
 * <AUTHOR>
 */
public class RegexUtils {

    // 验证邮箱
    private static final Pattern emailPattern = Pattern.compile("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*");

    // 验证手机
    private static final Pattern phonePattern = Pattern.compile("^1[34578]\\d{9}");

    // 验证以逗号分隔的字符串
    private static final Pattern splitByCommaPattern = Pattern.compile("^([^,]+[,])*([^,]+)$");

    /**
     * 判断是不是一个合法的电子邮件地址
     *
     * @param email 邮箱地址
     * @return true，正确的邮箱地址；false，错误的邮箱地址
     */
    public static boolean isEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return false;
        }
        return emailPattern.matcher(email).matches();
    }

    /**
     * 判断是不是一个合法的手机号
     *
     * @param phone 手机号
     * @return true，正确的手机号；false，错误的手机号
     */
    public static boolean isPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        return phonePattern.matcher(phone).matches();
    }

    /**
     * 判断数字
     *
     * @param str
     * @return
     */
    public static boolean isDigit(String str) {
        String regex = "^[0-9]+$";
        return str.matches(regex);
    }

    /**
     * 判断是否英文/数字/中文
     *
     * @param str
     * @return
     */
    public static boolean isLetterDigitChinese(String str) {
        String regex = "^[a-z0-9A-Z\u4e00-\u9fa5]+$";
        return str.matches(regex);
    }

    /**
     * 判断是以逗号分隔的字符串
     *
     * @param str
     * @return
     */
    public static boolean isSplitByComma(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return splitByCommaPattern.matcher(str).matches();
    }
}
