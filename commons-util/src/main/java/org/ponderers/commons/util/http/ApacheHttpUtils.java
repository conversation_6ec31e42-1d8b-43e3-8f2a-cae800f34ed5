package org.ponderers.commons.util.http;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.*;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * While HttpClient instances are thread safe and can be shared between multiple threads of execution,
 * it is highly recommended that each thread maintains its own dedicated instance of HttpContext .
 *
 * <AUTHOR>
 */
public class ApacheHttpUtils {

    private static final Logger log = LoggerFactory.getLogger(ApacheHttpUtils.class);

    public static final int AVAILABLE_PROCESSORS = Runtime.getRuntime().availableProcessors();

    private ApacheHttpUtils() {
    }

    public static CloseableHttpClient createClient() {
        return createClient(500, 1000, 3000);
    }

    public static CloseableHttpClient createClient(int connectionRequestTimeout, int connectTimeout, int socketTimeout) {
        return createClient(connectionRequestTimeout, connectTimeout, socketTimeout, Proxy.NO_PROXY);
    }

    public static CloseableHttpClient createClient(int connectionRequestTimeout, int connectTimeout, int socketTimeout, Proxy proxy) {
        final PoolingHttpClientConnectionManager connManager = buildConnectionManager();
        final RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(connectionRequestTimeout)
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(socketTimeout)
                .build();
        HttpClientBuilder httpClientBuilder = HttpClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(config)
                .setProxy(convertToHttpHost(proxy))
                .setRedirectStrategy(new LaxRedirectStrategy())
                .setRetryHandler(new DefaultHttpRequestRetryHandler(3, false));
        if (Objects.nonNull(connManager.getTotalStats())) {
            log.info("Pool statistics: {}", connManager.getTotalStats());
        }
        return httpClientBuilder.build();
    }

    private static HttpHost convertToHttpHost(Proxy proxy) {
        if (Proxy.Type.DIRECT.equals(proxy.type())) return null;
        InetSocketAddress address = (InetSocketAddress) proxy.address();
        return new HttpHost(address.getHostName(), address.getPort(), proxy.type().name());
    }

    private static PoolingHttpClientConnectionManager buildConnectionManager() {
        try {
            // 配置同时支持 HTTP 和 HTTPS
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, new TrustSelfSignedStrategy())
                    .build();
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", new SSLConnectionSocketFactory(sslContext))
                    .build();
            PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            // 配置每个链路的最大线程数
            connManager.setDefaultMaxPerRoute(AVAILABLE_PROCESSORS * 4);
            // 配置整个连接池最大线程数
            connManager.setMaxTotal(connManager.getDefaultMaxPerRoute() * 2);
            return connManager;
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            throw new ContextedRuntimeException("buildConnectionManager() invoke failure", e);
        }
    }

    @SneakyThrows
    public static InputStream httpGetSteam(final CloseableHttpClient httpClient, String url) {
        final HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = httpClient.execute(httpGet);
        final HttpEntity entity = response.getEntity();
        return Objects.nonNull(entity) ? entity.getContent() : null;
    }

    @SneakyThrows
    public static Optional<String> syncGet(CloseableHttpClient httpClient, HttpRequest httpRequest) {
        URIBuilder uriBuilder = new URIBuilder(httpRequest.getUrl());
        httpRequest.getQueryParameters().forEach(uriBuilder::addParameter);
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        httpRequest.getHeaders().forEach(httpGet::setHeader);
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            HttpEntity entity = response.getEntity();
            String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            EntityUtils.consumeQuietly(entity);
            return Optional.ofNullable(content);
        } catch (Exception e) {
            log.error("调用[syncGet]方法，调用异常。httpRequest: {}", httpRequest, e);
            return Optional.empty();
        }
    }

    /**
     * 请求URL资源
     * <p>
     * 首次请求失败后，重试3次，每次重试时间间隔为1s、2s、4s
     *
     * @param url 请求资源URL
     * @return 资源流
     */
    public static InputStream httpGetSteam(String url) {
        try {
            URL oUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) oUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            conn.setReadTimeout(50 * 1000);
            return conn.getInputStream();
        } catch (IOException e) {
            log.error("HTTP GET STREAM ERROR url: {}", url, e);
        }
        return null;
    }


    @SneakyThrows
    public static Optional<String> syncPostFormBody(CloseableHttpClient httpClient, HttpRequest httpRequest) {
        try {
            HttpPost httpPost = new HttpPost(httpRequest.getUrl());
            httpPost.addHeader("Content-type", "application/x-www-form-urlencoded; charset=utf-8");
            httpRequest.getHeaders().forEach(httpPost::addHeader);
            List<NameValuePair> nameValuePairs = httpRequest.getBodyParams().entrySet().stream()
                    .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue())).
                    collect(Collectors.toList());
            httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, StandardCharsets.UTF_8));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            if (!isSuccessful(response) || Objects.isNull(response.getEntity())) {
                log.error("调用[syncPostFormBody]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            return Optional.of(EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("调用[syncPostFormBody]方法，调用异常。httpRequest: {}", httpRequest, e);
            return Optional.empty();
        }
    }

    private static boolean isSuccessful(HttpResponse response) {
        if (response == null || response.getStatusLine() == null) return false;
        int statusCode = response.getStatusLine().getStatusCode();
        return statusCode >= HttpStatus.SC_OK && statusCode < HttpStatus.SC_MULTIPLE_CHOICES;
    }

    public static Optional<String> syncPostJsonBody(final CloseableHttpClient httpClient, HttpRequest httpRequest) {
        try {
            HttpPost httpPost = new HttpPost(httpRequest.getUrl());
            ContentType contentType = ContentType.create("application/json", "");
            httpPost.addHeader("Content-Type", contentType.toString());
            httpRequest.getHeaders().forEach(httpPost::addHeader);
            httpPost.setEntity(new StringEntity(httpRequest.getJsonBody(), StandardCharsets.UTF_8));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            if (!isSuccessful(response) || Objects.isNull(response.getEntity())) {
                log.error("调用[syncPostJsonBody]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            HttpEntity httpEntity = response.getEntity();
            String body = EntityUtils.toString(httpEntity);
            EntityUtils.consumeQuietly(httpEntity);
            return Optional.ofNullable(StringUtils.trimToEmpty(body));
        } catch (Exception e) {
            log.error("调用[syncPostJsonBody]方法，调用异常。httpRequest: {}", httpRequest, e);
            return Optional.empty();
        }
    }

}


