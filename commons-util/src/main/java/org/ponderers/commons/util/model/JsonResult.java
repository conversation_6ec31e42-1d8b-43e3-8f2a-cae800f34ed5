package org.ponderers.commons.util.model;

import lombok.Data;
import org.ponderers.commons.util.model.status.ResultCode;

import java.util.function.Function;

/**
 * 通用JSON数据结构
 *
 * <AUTHOR>
 */
@Data
public class JsonResult<T> {

    protected int code;
    protected String msg;
    protected T data;

    public JsonResult(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> JsonResult<T> result(ResultCode resultCode) {
        return new JsonResult<>(resultCode.getCode(), resultCode.getDesc(), null);
    }

    public static <T> JsonResult<T> result(int code, String msg, T data) {
        return new JsonResult<>(code, msg, data);
    }

    public static <T> JsonResult<T> result(ResultCode resultCode, String msg, T data) {
        return new JsonResult<>(resultCode.getCode(), msg, data);
    }

    public static <T> JsonResult<T> result(ResultCode resultCode, T data) {
        return new JsonResult<>(resultCode.getCode(), resultCode.getDesc(), data);
    }

    public static <T> JsonResult<T> result(ResultCode code, Function<ResultCode, String> function, T data) {
        return result(code, function.apply(code), data);
    }
}


