package org.ponderers.commons.util.model.status;

import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @see <a href="https://developer.apple.com/documentation/appstorereceipts/status">App Store Receipts</a>
 */
@Getter
@ToString
public enum ReceiptResultCode implements ResultCode {

    STATUS_0(0, "This receipt is valid."),

    STATUS_21000(21000, "The request to the App Store was not made using the HTTP POST request method."),
    STATUS_21001(21001, "This status code is no longer sent by the App Store."),
    STATUS_21002(21002, "The data in the receipt-data property was malformed or the service experienced a temporary issue. Try again."),
    STATUS_21003(21003, "The receipt could not be authenticated."),
    STATUS_21004(21004, "The shared secret you provided does not match the shared secret on file for your account."),
    STATUS_21005(21005, "The receipt server was temporarily unable to provide the receipt. Try again."),
    STATUS_21006(21006, "This receipt is valid but the subscription has expired. When this status code is returned to your server, the receipt data is also decoded and returned as part of the response. Only returned for iOS 6-style transaction receipts for auto-renewable subscriptions."),
    STATUS_21007(21007, "This receipt is from the test environment, but it was sent to the production environment for verification."),
    STATUS_21008(21008, "This receipt is from the production environment, but it was sent to the test environment for verification."),
    STATUS_21009(21009, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21010(21010, "The user account cannot be found or has been deleted."),

    // Status codes 21100-21199 are various internal data access errors.
    STATUS_21100(21100, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21101(21101, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21102(21102, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21103(21103, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21104(21104, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21105(21105, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21106(21106, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21107(21107, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21108(21108, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21109(21109, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21110(21110, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21111(21111, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21112(21112, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21113(21113, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21114(21114, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21115(21115, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21116(21116, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21117(21117, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21118(21118, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21119(21119, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21120(21120, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21121(21121, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21122(21122, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21123(21123, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21124(21124, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21125(21125, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21126(21126, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21127(21127, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21128(21128, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21129(21129, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21130(21130, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21131(21131, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21132(21132, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21133(21133, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21134(21134, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21135(21135, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21136(21136, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21137(21137, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21138(21138, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21139(21139, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21140(21140, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21141(21141, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21142(21142, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21143(21143, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21144(21144, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21145(21145, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21146(21146, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21147(21147, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21148(21148, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21149(21149, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21150(21150, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21151(21151, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21152(21152, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21153(21153, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21154(21154, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21155(21155, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21156(21156, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21157(21157, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21158(21158, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21159(21159, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21160(21160, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21161(21161, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21162(21162, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21163(21163, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21164(21164, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21165(21165, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21166(21166, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21167(21167, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21168(21168, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21169(21169, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21170(21170, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21171(21171, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21172(21172, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21173(21173, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21174(21174, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21175(21175, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21176(21176, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21177(21177, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21178(21178, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21179(21179, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21180(21180, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21181(21181, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21182(21182, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21183(21183, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21184(21184, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21185(21185, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21186(21186, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21187(21187, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21188(21188, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21189(21189, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21190(21190, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21191(21191, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21192(21192, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21193(21193, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21194(21194, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21195(21195, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21196(21196, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21197(21197, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21198(21198, Constants.INTERNAL_DATA_ACCESS_ERROR),
    STATUS_21199(21199, Constants.INTERNAL_DATA_ACCESS_ERROR);

    private final int code;
    private final String desc;

    ReceiptResultCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isValidReceipt(int code) {
        return STATUS_0.getCode() == code;
    }

    private static class Constants {
        private static final String INTERNAL_DATA_ACCESS_ERROR = "Internal data access error. Try again later.";
    }
}
