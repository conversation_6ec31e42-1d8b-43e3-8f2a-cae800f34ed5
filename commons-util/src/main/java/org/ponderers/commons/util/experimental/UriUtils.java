package org.ponderers.commons.util.experimental;

import lombok.SneakyThrows;
import org.apache.http.client.utils.URIBuilder;

import java.net.URI;
import java.nio.charset.StandardCharsets;

public class UriUtils {

    private UriUtils() {
    }

    @SneakyThrows
    public static String appendUri(String uri, String appendQuery) {
        URI oldUri = new URI(uri);
        String newQuery = oldUri.getQuery();
        if (newQuery == null) {
            newQuery = appendQuery;
        } else {
            newQuery += "&" + appendQuery;
        }
        URI newUri = new URI(oldUri.getScheme(), oldUri.getAuthority(),
                oldUri.getPath(), newQuery, oldUri.getFragment());
        return newUri.toString();
    }

    @SneakyThrows
    public static String appendUri(String uri, String param, String value) {
        return new URIBuilder(uri, StandardCharsets.UTF_8).addParameter(param, value).build().toString();
    }
}
