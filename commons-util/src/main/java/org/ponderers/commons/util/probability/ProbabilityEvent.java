package org.ponderers.commons.util.probability;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public final class ProbabilityEvent {

    private String prizeId;
    private String prizeName;
    private long prizeNum;
    private BigDecimal p0;
    private BigDecimal p1;
    private int maxInput;
    private BigDecimal probability;

    public void calculateProbability(int input) {
        if (input <= 0) {
            this.probability = this.p0;
            return;
        }
        if (input >= maxInput) {
            this.probability = this.p1;
            return;
        }
        this.probability = this.p1.subtract(this.p0)
                .divide(BigDecimal.valueOf(maxInput), 6, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(input))
                .add(this.p0).setScale(6, RoundingMode.HALF_UP);
    }
}
