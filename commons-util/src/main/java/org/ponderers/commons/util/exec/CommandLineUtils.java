package org.ponderers.commons.util.exec;

import lombok.SneakyThrows;
import org.apache.commons.exec.*;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.function.Consumer;

public class CommandLineUtils {

    private static final Logger log = LoggerFactory.getLogger(CommandLineUtils.class);

    private CommandLineUtils() {
    }

    @SneakyThrows
    public static void asyncExecute(CommandLine commandLine, long timeoutMills, Consumer<Integer> onSuccess, Consumer<ExecuteException> onFailure) {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        ByteArrayOutputStream errStream = new ByteArrayOutputStream();
        PumpStreamHandler streamHandler = new PumpStreamHandler(outStream, errStream);
        ExecuteWatchdog watchdog = new ExecuteWatchdog(timeoutMills);
        Executor executor = new DefaultExecutor();
        executor.setWatchdog(watchdog);
        executor.setStreamHandler(streamHandler);
        executor.execute(commandLine, new ExecuteResultHandler() {
            @Override
            public void onProcessComplete(int exitValue) {
                onSuccess.accept(exitValue);
            }

            @Override
            public void onProcessFailed(ExecuteException e) {
                onFailure.accept(e);
            }
        });
        Thread.currentThread().join(timeoutMills);
        String outString = new String(outStream.toByteArray(), StandardCharsets.UTF_8);
        String errString = new String(errStream.toByteArray(), StandardCharsets.UTF_8);
        log.warn("asyncExecute. outString: {}, errString: {}", outString, errString);
    }

    @SneakyThrows
    public static int syncExecute(CommandLine commandLine, long timeoutMills) {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        ByteArrayOutputStream errStream = new ByteArrayOutputStream();
        PumpStreamHandler streamHandler = new PumpStreamHandler(outStream, errStream);
        ExecuteWatchdog watchdog = new ExecuteWatchdog(timeoutMills);
        Executor executor = new DefaultExecutor();
        executor.setStreamHandler(streamHandler);
        executor.setWatchdog(watchdog);
        int exitValue = executor.execute(commandLine);
        String outString = new String(outStream.toByteArray(), StandardCharsets.UTF_8);
        String errString = new String(errStream.toByteArray(), StandardCharsets.UTF_8);
        log.warn("syncExecute. outString: {}, errString: {}", outString, errString);
        return exitValue;
    }
}
