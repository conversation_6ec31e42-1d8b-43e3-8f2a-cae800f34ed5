package org.ponderers.commons.util.logback.converter;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

import java.util.UUID;

/**
 * Logback converter for %uuid
 * <pre>{@code
 * <conversionRule conversionWord="uuid" converterClass="org.ponderers.commons.util.logback.converter.UUIDConverter" />
 * }</pre>
 *
 * <AUTHOR>
 */
public class UUIDConverter extends ClassicConverter {
    @Override
    public String convert(ILoggingEvent event) {
        return UUID.randomUUID().toString().replace("-", "");
    }
}