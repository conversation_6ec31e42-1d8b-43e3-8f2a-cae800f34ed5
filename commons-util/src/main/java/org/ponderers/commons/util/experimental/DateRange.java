package org.ponderers.commons.util.experimental;

import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.time.DateUtils;
import org.ponderers.commons.util.time.DateHelper;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Data
public class DateRange {

    @ToString.Exclude
    private final Date startDate;
    @ToString.Exclude
    private final Date endDate;

    @ToString.Include(name = "startDate")
    private String formatStartDate() {
        return DateHelper.format(startDate);
    }

    @ToString.Include(name = "endDate")
    private String formatEndDate() {
        return DateHelper.format(endDate);
    }

    public DateRange(int delayMinutes, int coverMinutes) {
        Date current = new Date();
        this.endDate = DateUtils.addMinutes(current, -1 * delayMinutes);
        this.startDate = DateUtils.addMinutes(endDate, -1 * coverMinutes);
    }

    public List<Date> getDatesBetween() {
        LocalDate localStartDate = DateHelper.toLocalDate(startDate);
        LocalDate localEndDate = DateHelper.toLocalDate(endDate);
        long numOfDaysBetween = ChronoUnit.DAYS.between(localStartDate, localEndDate);
        return IntStream.iterate(0, i -> i + 1)
                .limit(numOfDaysBetween)
                .mapToObj(localStartDate::plusDays)
                .map(DateHelper::toDate)
                .filter(date -> date.after(startDate))
                .filter(date -> date.before(endDate))
                .collect(Collectors.toList());
    }
}
