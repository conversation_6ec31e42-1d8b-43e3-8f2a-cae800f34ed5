package org.ponderers.commons.util.crypto;

import org.apache.commons.lang3.exception.ContextedRuntimeException;

import javax.crypto.*;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

/**
 * Triple DEC Utils
 *
 * <AUTHOR>
 */
public class TripleDESUtils {

    private static final byte[] IV = {0x12, 0x34, 0x56, 0x78, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF};

    private TripleDESUtils() {
    }

    /**
     * Three Des Decrypt
     *
     * @param plainText encrypt text used to decrypt
     * @param secretKey decrypt secret key
     * @return plain text
     */
    public static String encrypt(String plainText, byte[] secretKey) {
        try {
            DESedeKeySpec spec = new DESedeKeySpec(secretKey);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("desede");
            Key key = keyFactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(IV);
            cipher.init(Cipher.ENCRYPT_MODE, key, ips);
            byte[] encryptData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            byte[] encodedBytes = Base64.getEncoder().encode(encryptData);
            return new String(encodedBytes);
        } catch (InvalidKeyException | NoSuchAlgorithmException | InvalidAlgorithmParameterException |
                 NoSuchPaddingException |
                 BadPaddingException | InvalidKeySpecException | IllegalBlockSizeException e) {
            throw new ContextedRuntimeException("Encrypt plain text failure", e).addContextValue("plainText", plainText);
        }
    }

    /**
     * Three Des Decrypt
     *
     * @param encryptText encrypt text used to decrypt
     * @param secretKey   decrypt secret key
     * @return plain text
     */
    public static String decrypt(String encryptText, byte[] secretKey) {
        try {
            DESedeKeySpec spec = new DESedeKeySpec(secretKey);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("desede");
            Key key = keyFactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(IV);
            cipher.init(Cipher.DECRYPT_MODE, key, ips);
            byte[] decodedBytes = Base64.getDecoder().decode(encryptText.getBytes());
            byte[] decryptData = cipher.doFinal(decodedBytes);
            return new String(decryptData, StandardCharsets.UTF_8);
        } catch (InvalidKeyException | NoSuchAlgorithmException | InvalidAlgorithmParameterException |
                 NoSuchPaddingException |
                 BadPaddingException | InvalidKeySpecException | IllegalBlockSizeException e) {
            throw new ContextedRuntimeException("Decrypt encrypt text failure", e).addContextValue("encryptText", encryptText);
        }
    }
}
