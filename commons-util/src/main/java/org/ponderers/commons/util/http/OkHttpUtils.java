package org.ponderers.commons.util.http;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.google.common.base.Preconditions;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.net.Proxy;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Http调用
 *
 * <AUTHOR>
 */
public class OkHttpUtils {

    private static final Logger log = LoggerFactory.getLogger(OkHttpUtils.class);

    public static final MediaType MEDIA_TYPE_JSON = MediaType.parse("application/json; charset=utf-8");
    public static final MediaType MEDIA_TYPE_FORM = MediaType.parse("application/x-www-form-urlencoded; charset=utf-8");

    private static final int DEFAULT_STOP_AFTER_ATTEMPT = 3;

    public static OkHttpClient createClient() {
        return createClient(500, 1000, 1000);
    }

    public static OkHttpClient createClient(long connectTimeoutMills, long readTimeoutMills, long writeTimeoutMills) {
        return createClient(connectTimeoutMills, readTimeoutMills, writeTimeoutMills, Proxy.NO_PROXY);
    }

    public static OkHttpClient createClient(long connectTimeoutMills, long readTimeoutMills, long writeTimeoutMills, Proxy proxy) {
        return new OkHttpClient.Builder()
                .proxy(proxy)
                .connectTimeout(connectTimeoutMills, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeoutMills, TimeUnit.MILLISECONDS)
                .writeTimeout(writeTimeoutMills, TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    private OkHttpUtils() {
    }

    public static Optional<String> syncGet(final OkHttpClient client, final HttpRequest httpRequest) {
        Retryer<Optional<String>> retryer = RetryerBuilder.<Optional<String>>newBuilder()
                .retryIfExceptionOfType(IOException.class)
                .retryIfRuntimeException()
                .withStopStrategy(StopStrategies.stopAfterAttempt(DEFAULT_STOP_AFTER_ATTEMPT))
                .build();
        try {
            return retryer.call(() -> doSyncGet(client, httpRequest));
        } catch (ExecutionException | RetryException e) {
            log.warn("调用[syncGet]方法，重试异常。", e);
        }
        return Optional.empty();
    }

    public static Optional<String> syncPostFormBody(final OkHttpClient client, final HttpRequest httpRequest) {
        Retryer<Optional<String>> retryer = RetryerBuilder.<Optional<String>>newBuilder()
                .retryIfExceptionOfType(IOException.class)
                .retryIfRuntimeException()
                .withStopStrategy(StopStrategies.stopAfterAttempt(DEFAULT_STOP_AFTER_ATTEMPT))
                .build();
        try {
            return retryer.call(() -> doSyncPostFormBody(client, httpRequest));
        } catch (ExecutionException | RetryException e) {
            log.warn("调用[syncPostFormBody]方法，重试异常。", e);
        }
        return Optional.empty();
    }

    public static Optional<String> syncPostJsonBody(final OkHttpClient client, final HttpRequest httpRequest) {
        Retryer<Optional<String>> retryer = RetryerBuilder.<Optional<String>>newBuilder()
                .retryIfExceptionOfType(IOException.class)
                .retryIfRuntimeException()
                .withStopStrategy(StopStrategies.stopAfterAttempt(DEFAULT_STOP_AFTER_ATTEMPT))
                .build();
        try {
            return retryer.call(() -> doSyncPostJsonBody(client, httpRequest));
        } catch (ExecutionException | RetryException e) {
            log.warn("调用[syncPostJsonBody]方法，重试异常。", e);
        }
        return Optional.empty();
    }

    public static void asyncGet(final OkHttpClient client, final HttpRequest httpRequest,
                                Consumer<Optional<String>> callback) {
        Request request = new Request.Builder()
                .url(buildHttpUrl(httpRequest.getUrl(), httpRequest.getQueryParameters()))
                .build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@Nonnull Call call, @Nonnull IOException e) {
                log.error("调用doAsyncGet()方法，调用异常。httpRequest: {}, errorMessage: {}", httpRequest, ExceptionUtils.getMessage(e));
                callback.accept(Optional.empty());
            }
            @Override
            public void onResponse(@Nonnull Call call, @Nonnull Response response) throws IOException {
                log.info("调用doAsyncGet()方法，调用成功。response：{}", response);
                if (Objects.isNull(response.body())) {
                    log.error("调用doAsyncGet()方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                    callback.accept(Optional.empty());
                }
                callback.accept(Optional.of(response.body().string()));
            }
        });
    }

    private static HttpUrl buildHttpUrl(String url, Map<String, String> queryParameters) {
        HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
        queryParameters.forEach(builder::addQueryParameter);
        return builder.build();
    }

    private static RequestBody buildRequestBody(Map<String, String> bodyParams) {
        String content = bodyParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        return RequestBody.create(content, MEDIA_TYPE_FORM);
    }

    private static Optional<String> doSyncGet(final OkHttpClient client, final HttpRequest httpRequest) {
        Request request = new Request.Builder()
                .url(buildHttpUrl(httpRequest.getUrl(), httpRequest.getQueryParameters()))
                .headers(Headers.of(httpRequest.getHeaders()))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful() || Objects.isNull(response.body()) || StringUtils.isBlank(response.body().toString())) {
                log.error("调用[doSyncGet]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            return Optional.of(response.body().string());
        } catch (IOException e) {
            log.error("调用[doSyncGet]方法，调用异常。httpRequest: {}", httpRequest, e);
            throw new ContextedRuntimeException(ExceptionUtils.getMessage(e), e);
        }
    }

    private static Optional<String> doSyncPostFormBody(final OkHttpClient client, final HttpRequest httpRequest) {
        Request request = new Request.Builder()
                .url(buildHttpUrl(httpRequest.getUrl(), httpRequest.getQueryParameters()))
                .headers(Headers.of(httpRequest.getHeaders()))
                .post(buildRequestBody(httpRequest.getBodyParams()))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful() || Objects.isNull(response.body()) || StringUtils.isBlank(response.body().toString())) {
                log.error("调用[doSyncPostFormBody]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            return Optional.of(response.body().string());
        } catch (IOException e) {
            log.error("调用[doSyncPostFormBody]方法，调用异常。httpRequest: {}", httpRequest, e);
            throw new ContextedRuntimeException(ExceptionUtils.getMessage(e), e);
        }
    }

    private static Optional<String> doSyncPostJsonBody(final OkHttpClient client, final HttpRequest httpRequest) {
        Preconditions.checkNotNull(httpRequest.getJsonBody(), "json body cannot be null");
        Request request = new Request.Builder()
                .url(buildHttpUrl(httpRequest.getUrl(), httpRequest.getQueryParameters()))
                .headers(Headers.of(httpRequest.getHeaders()))
                .post(RequestBody.create(httpRequest.getJsonBody(), MEDIA_TYPE_JSON))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful() || Objects.isNull(response.body())) {
                log.error("调用[doSyncPostJsonBody]方法，调用失败。httpRequest: {}, response: {}", httpRequest, response);
                return Optional.empty();
            }
            return Optional.of(response.body().string());
        } catch (IOException e) {
            log.error("调用[doSyncPostJsonBody]方法，重试异常。httpRequest: {}", httpRequest, e);
            throw new ContextedRuntimeException(ExceptionUtils.getMessage(e), e);
        }
    }
}
