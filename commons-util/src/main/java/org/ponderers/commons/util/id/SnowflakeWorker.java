package org.ponderers.commons.util.id;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.exception.ContextedException;
import org.apache.curator.RetryPolicy;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.RetryUntilElapsed;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;
import org.ponderers.commons.util.json.JsonUtils;
import org.ponderers.commons.util.time.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

public class SnowflakeWorker {

    private static final Logger log = LoggerFactory.getLogger(SnowflakeWorker.class);

    /**
     * Worker节点key，例如：ip:port形式保存
     */
    private final String workerIpAndPort;

    /**
     * Snowflake workerId
     */
    private int workerId;

    /**
     * 在Worker节点文件系统上缓存一个workId值，保证zk失效的情况下机器能够正常重启
     */
    private static final String PROP_PATH = Paths.get(System.getProperty("java.io.tmpdir"),
            "/worker/conf/{port}/snowflake_worker.properties").toString();
    /**
     * 保存所有数据持久的节点
     */
    public static final String FOREVER_PATH = Paths.get("/snowflake/worker/forever").toString();
    private final String workerIp;
    private final int workerPort;
    private final String zkConnectionString;
    private final int connectionTimeoutMs;
    private final int sessionTimeoutMs;
    private long lastUpdateTime;

    public SnowflakeWorker(String workerIp, int workerPort, String zkConnectionString) {
        this(workerIp, workerPort, zkConnectionString, 10000, 60000);
    }

    public SnowflakeWorker(String workerIp, int workerPort, String zkConnectionString,
                           int connectionTimeoutMs, int sessionTimeoutMs) {
        this.workerIp = workerIp;
        this.workerPort = workerPort;
        this.workerIpAndPort = workerIp + ":" + workerPort;
        this.zkConnectionString = zkConnectionString;
        this.connectionTimeoutMs = connectionTimeoutMs;
        this.sessionTimeoutMs = sessionTimeoutMs;
        initialized();
    }

    /**
     * <pre>
     * [zk: localhost:2181(CONNECTED) 19] ls -R /snowflake
     * /snowflake
     * /snowflake/worker
     * /snowflake/worker/forever
     * /snowflake/worker/forever/*************:8888-0000000000
     * /snowflake/worker/forever/*************:9999-0000000001
     * </pre>
     */
    @SneakyThrows
    public void initialized() {
        final CuratorFramework curator = createWithOptions(zkConnectionString, connectionTimeoutMs, sessionTimeoutMs);
        curator.start();
        Stat stat = curator.checkExists().forPath(FOREVER_PATH);
        if (stat == null) {
            // 不存在根节点，机器第一次启动。创建/snowflake/worker/forever/ip:port-000000000并上传数据
            String zkNodePath = createPersistentSequentialNode(curator);
            // 定时上报本机时间给forever节点
            scheduledUploadData(curator, zkNodePath);
            return;
        }
        // ip:port-sequence
        List<String> znodeNames = curator.getChildren().forPath(FOREVER_PATH);
        List<WorkerNode> workerNodes = znodeNames.stream().map(WorkerNode::fromZnodeName).toList();

        // 清理过期worker节点
        List<WorkerNode> filteredWorkerNodes = workerNodes.stream()
                .filter(workerNode -> !removeWorkerNode(curator, workerNode.getZnodePath()))
                .toList();

        // ip:port->ip:port-sequence
        Map<String, WorkerNode> ipPortNodeMap = filteredWorkerNodes.stream()
                .collect(Collectors.toMap(WorkerNode::getIpPort, Function.identity(), (o1, o2) -> o1));
        // sequence->ip:port-sequence
        Map<Integer, WorkerNode> sequenceNodeMap = filteredWorkerNodes.stream()
                .collect(Collectors.toMap(WorkerNode::getSequence, Function.identity(), (o1, o2) -> o1));

        // 检查是否有属于自己的根节点
        boolean exist = ipPortNodeMap.containsKey(workerIpAndPort);
        if (exist) {
            WorkerNode workerNode = ipPortNodeMap.get(workerIpAndPort);
            this.workerId = workerNode.getWorkerId();
            scheduledUploadData(curator, workerNode.getZnodePath());
            log.info("[Old NODE]find forever node have this endpoint ip-{} port-{} workerId-{} childNode and start SUCCESS", workerIp, workerPort, this.workerId);
            return;
        }
        // 新节点需要创建持久节点
        String zkNodePath = createPersistentSequentialNode(curator);
        WorkerNode workerNode = WorkerNode.fromZnodePath(zkNodePath);
        Optional<Map.Entry<Integer, WorkerNode>> optionalEntry = sequenceNodeMap.entrySet().stream()
                .filter(entry -> entry.getValue().getWorkerId() != workerNode.getWorkerId())
                .findFirst();
        if (optionalEntry.isPresent()) {
            removeWorkerNode(curator, zkNodePath);
            Map.Entry<Integer, WorkerNode> entry = optionalEntry.get();
            throw new ContextedException("注册workNode失败，workerId已存在")
                    .addContextValue("workNode", entry.getValue());
        }
        this.workerId = workerNode.getWorkerId();
        scheduledUploadData(curator, zkNodePath);
        log.info("[New NODE]can not find node on forever node that endpoint ip-{} port-{} workerId-{} " +
                "create own node on forever node and start SUCCESS ", workerIp, workerPort, this.workerId);

    }

    private static boolean removeWorkerNode(CuratorFramework curator, String znodePath) {
        try {
            byte[] buffer = curator.getData().forPath(znodePath);
            String data = new String(buffer, StandardCharsets.UTF_8);
            WorkerNodeData workerNodeData = JsonUtils.parseObject(data, WorkerNodeData.class);
            if (System.currentTimeMillis() - workerNodeData.getTimestamp() > TimeUnit.MINUTES.toMillis(1)) {
                log.warn("清理过期worker节点，开始清理。workerInfo: {}", workerNodeData);
                curator.delete().forPath(znodePath);
                return true;
            }
        } catch (Exception e) {
            log.error("清理过期worker节点，清理失败。znodePath: {}", znodePath, e);
        }
        return false;
    }

    private void scheduledUploadData(final CuratorFramework curator, final String zkNodePath) {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("schedule-upload-time" + "-%d")
                .setDaemon(true)
                .build();
        // 禁止关闭服务
        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor(threadFactory);
        executorService.scheduleWithFixedDelay(() -> updateNewData(curator, zkNodePath), 1L, 3L, TimeUnit.SECONDS);
    }

    /**
     * 创建持久顺序节点 ,并把节点数据放入Data
     */
    private String createPersistentSequentialNode(CuratorFramework curator) throws Exception {
        String path = FOREVER_PATH + "/" + workerIpAndPort + "-";
        String data = JsonUtils.toJSONString(buildWorkerNodeData());
        return curator.create()
                .creatingParentsIfNeeded()
                .withMode(CreateMode.PERSISTENT_SEQUENTIAL)
                .forPath(path, data.getBytes());
    }

    private void updateNewData(CuratorFramework curator, String path) {
        try {
            if (System.currentTimeMillis() < lastUpdateTime) {
                return;
            }
            WorkerNodeData workerNodeData = buildWorkerNodeData();
            String data = JsonUtils.toJSONString(buildWorkerNodeData());
            log.info("定时上报本机时间给forever节点。path: {}, workerNodeData: {}", path, workerNodeData);
            curator.setData().forPath(path, data.getBytes());
            lastUpdateTime = workerNodeData.getTimestamp();
        } catch (Exception e) {
            log.error("update workerNode data error. path: {}", path, e);
        }
    }

    /**
     * 构建需要上传的数据
     */
    @SneakyThrows
    private WorkerNodeData buildWorkerNodeData() {
        long currentTimeMillis = System.currentTimeMillis();
        return WorkerNodeData.builder()
                .ipPort(workerIpAndPort)
                .ip(workerIp)
                .port(workerPort)
                .time(DateHelper.format(currentTimeMillis))
                .timestamp(currentTimeMillis)
                .build();
    }

    private CuratorFramework createWithOptions(String connectString, int connectionTimeoutMs, int sessionTimeoutMs) {
        RetryPolicy retryPolicy = new RetryUntilElapsed(1000, 4);
        return CuratorFrameworkFactory.builder()
                .connectString(connectString)
                .retryPolicy(retryPolicy)
                .connectionTimeoutMs(connectionTimeoutMs)
                .sessionTimeoutMs(sessionTimeoutMs)
                .build();
    }

    /**
     * WorkerNode信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class WorkerNode {

        private String znodePath;
        private String znodeName;
        private String ipPort;
        private int sequence;
        private int workerId;

        public static WorkerNode fromZnodeName(String znodeName) {
            String[] parts = znodeName.split("-");
            Validate.validIndex(parts, 1, "解析znodeName失败");
            int sequence = Integer.parseInt(parts[1]);
            String ipPort = parts[0];
            String znodePath = Paths.get(FOREVER_PATH, znodeName).toString();
            return WorkerNode.builder()
                    .znodePath(znodePath)
                    .znodeName(znodeName)
                    .ipPort(ipPort)
                    .sequence(sequence)
                    .workerId(sequence % 32)
                    .build();
        }

        public static WorkerNode fromZnodePath(String znodePath) {
            String znodeName = StringUtils.removeStart(znodePath, FOREVER_PATH);
            return WorkerNode.fromZnodeName(znodeName);
        }
    }

    /**
     * WorkerNode数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class WorkerNodeData {
        private String ip;
        private int port;
        private long timestamp;
        private String time;
        private String ipPort;
    }

    public int getWorkerId() {
        return workerId;
    }

}
