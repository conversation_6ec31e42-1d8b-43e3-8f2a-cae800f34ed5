package org.ponderers.commons.util.time;

import lombok.SneakyThrows;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import javax.annotation.Nonnull;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Date Helper
 *
 * <AUTHOR>
 */
public final class DateHelper {

    private static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String YEAR_MONTH_PATTERN = "yyyyMM";

    private DateHelper() {
    }

    /**
     * <p>Checks if two date objects are on the same month ignoring time.</p>
     *
     * @param date1 the first date, not altered, not null
     * @param date2 the second date, not altered, not null
     * @return true if they represent the same month
     * @throws IllegalArgumentException if either date is <code>null</code>
     */
    public static boolean isSameMonth(@Nonnull final Date date1, @Nonnull final Date date2) {
        final Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        final Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return isSameMonth(cal1, cal2);
    }

    /**
     * <p>Checks if two calendar objects are on the same month ignoring time.</p>
     *
     * @param cal1 the first calendar, not altered, not null
     * @param cal2 the second calendar, not altered, not null
     * @return true if they represent the same day
     * @throws IllegalArgumentException if either calendar is <code>null</code>
     */
    public static boolean isSameMonth(@Nonnull final Calendar cal1, @Nonnull final Calendar cal2) {
        return cal1.get(Calendar.ERA) == cal2.get(Calendar.ERA) &&
                cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
    }

    /**
     * <p>Current timestamp seconds.</p>
     *
     * @return timestamp(seconds)
     */
    public static long currentSeconds() {
        return Instant.now().getEpochSecond();
    }

    /**
     * <p>Formats a date/time into a specific pattern.</p>
     *
     * @param date the date to format, not null
     * @return the formatted date
     */
    public static String format(@Nonnull Date date) {
        return format(date.getTime());
    }

    public static String format(@Nonnull Date date, String pattern) {
        return format(date.getTime(), pattern);
    }

    public static String format(@Nonnull LocalDate localDate) {
        return format(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    public static String format(@Nonnull LocalDate localDate, String pattern) {
        return format(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), pattern);
    }

    public static String format(@Nonnull LocalDateTime localDateTime) {
        return format(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    public static String format(@Nonnull LocalDateTime localDateTime, String pattern) {
        return format(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), pattern);
    }

    public static String format(long mills) {
        return format(mills, DATETIME_PATTERN);
    }

    public static String format(final long mills, final String pattern) {
        return DateFormatUtils.format(mills, pattern);
    }

    public static String formatYearMonth(@Nonnull Date date) {
        return formatYearMonth(date.getTime());
    }

    public static String formatYearMonth(long mills) {
        return DateFormatUtils.format(mills, YEAR_MONTH_PATTERN);
    }

    @SneakyThrows
    public static Date parseDate(String str) {
        return DateUtils.parseDate(str, DATETIME_PATTERN);
    }

    public static Date atStartOfDay(@Nonnull Date date) {
        LocalDateTime localDateTime = LocalDateTime.from(toLocalDateTime(date)).with(LocalTime.MIN);
        return toDate(localDateTime);
    }

    public static Date atEndOfDay(@Nonnull Date date) {
        LocalDateTime localDateTime = LocalDateTime.from(toLocalDateTime(date)).with(LocalTime.MAX);
        return toDate(localDateTime);
    }

    public static Date atStartOfWeek(@Nonnull Date date) {
        LocalDateTime localDateTime = LocalDateTime.from(toLocalDateTime(date))
                .with(LocalTime.MIN)
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return toDate(localDateTime);
    }

    public static Date atEndOfWeek(@Nonnull Date date) {
        LocalDateTime localDateTime =  LocalDateTime.from(toLocalDateTime(date))
                .with(LocalTime.MAX)
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        return toDate(localDateTime);
    }

    public static Date atStartOfMonth(@Nonnull Date date) {
        LocalDateTime localDateTime = LocalDateTime.from(toLocalDateTime(date))
                .with(LocalTime.MIN)
                .with(TemporalAdjusters.firstDayOfMonth());
        return toDate(localDateTime);
    }

    public static Date atEndOfMonth(@Nonnull Date date) {
        LocalDateTime localDateTime = LocalDateTime.from(toLocalDateTime(date))
                .with(LocalTime.MAX)
                .with(TemporalAdjusters.lastDayOfMonth());
        return toDate(localDateTime);
    }

    public static Duration getDuration(@Nonnull Date startDate, @Nonnull Date endDate) {
        return Duration.between(startDate.toInstant(), endDate.toInstant());
    }

    public static String humanReadableFormat(long mills) {
        return humanReadableFormat(Duration.ofMillis(mills));
    }

    public static String humanReadableFormat(Duration duration) {
        return duration.toString()
                .substring(2)
                .replaceAll("(\\d[HMS])(?!$)", "$1 ")
                .toLowerCase();
    }

    public static boolean isCrossMonth(@Nonnull Date date, int deltaMinutes) {
        return !isSameMonth(date, DateUtils.addMinutes(date, -deltaMinutes));
    }

    public static LocalDate toLocalDate(@Nonnull Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime toLocalDateTime(@Nonnull Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime toLocalDateTime(long epochMilli) {
        return Instant.ofEpochMilli(epochMilli).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Date toDate(@Nonnull LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(@Nonnull LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static List<Date> getDatesBetween(Date startDate, Date endDate) {
        long numOfDaysBetween = ChronoUnit.DAYS.between(toLocalDate(startDate), toLocalDate(endDate));
        return IntStream.iterate(0, i -> i + 1)
                .limit(numOfDaysBetween)
                .mapToObj(i -> toLocalDate(startDate).plusDays(i))
                .map(DateHelper::toDate)
                .collect(Collectors.toList());
    }

    public static int getWeekOfYear(Date date) {
        LocalDate localDate = toLocalDate(date);
        // 解决使用 ISO 周数计算时，跨年或特殊情况下会出现“同一自然周被分割到不同周数”的现象
        return localDate.get(WeekFields.of(DayOfWeek.MONDAY, 1).weekOfYear());
    }

    public static int getQuarterOfYear(Date date) {
        return getQuarterOfYear(date.getTime());
    }

    public static int getQuarterOfYear(long mills) {
        return toLocalDateTime(mills).get(IsoFields.QUARTER_OF_YEAR);
    }

    @SneakyThrows
    public static void sleep(long timeout, TimeUnit timeUnit) {
        timeUnit.sleep(timeout);
    }

}
