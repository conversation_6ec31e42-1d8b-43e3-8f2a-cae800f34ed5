package org.ponderers.commons.util.net;

import com.google.common.collect.Lists;
import com.google.common.net.InetAddresses;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.apache.commons.validator.routines.InetAddressValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NetworkInterface;
import java.net.Socket;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;

/**
 * IP Utils
 *
 * <AUTHOR>
 */
public class IpUtils {

    private static final Logger log = LoggerFactory.getLogger(IpUtils.class);

    private IpUtils() {
    }

    /**
     * 获取服务端IP地址
     *
     * @return 服务端IP地址
     */
    public static String getServerIpAddress(String... preferIpPrefixes) {
        String hostAddress = StringUtils.EMPTY;
        try {
            List<String> candidateHostAddressList = Lists.newArrayList();
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                // filters out 127.0.0.1 and inactive interfaces
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress inetAddress = addresses.nextElement();
                    if (isValidAddress(inetAddress.getHostAddress())) {
                        hostAddress = inetAddress.getHostAddress();
                        if (StringUtils.startsWithAny(hostAddress, preferIpPrefixes)) {
                            candidateHostAddressList.add(hostAddress);
                        }
                    }
                }
            }
            hostAddress = Arrays.stream(preferIpPrefixes)
                    .map(preferIpPrefix -> candidateHostAddressList.stream()
                            .filter(candidateHostAddress -> StringUtils.startsWith(candidateHostAddress, preferIpPrefix))
                            .findFirst().orElse(null))
                    .filter(Objects::nonNull)
                    .findFirst().orElse(hostAddress);
        } catch (Exception e) {
            log.warn("获取服务端IP地址失败", e);
        }
        return hostAddress;
    }

    /**
     * 获取客户端IP地址
     *
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String hostAddress = request.getHeader("x-forwarded-for");
        if (StringUtils.isBlank(hostAddress)) {
            hostAddress = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(hostAddress)) {
            hostAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(hostAddress)) {
            hostAddress = request.getRemoteAddr();
            if ("127.0.0.1".equals(hostAddress) || "0:0:0:0:0:0:0:1".equals(hostAddress)) {
                hostAddress = getServerIpAddress();
            }
        }
        if (hostAddress != null) {
            String[] ips = hostAddress.split(",");
            for (String ip : ips) {
                if (isValidAddress(ip.trim())) {
                    hostAddress = ip.trim();
                    break;
                }
            }
        }
        return hostAddress;
    }

    /**
     * Checks if the specified string is a valid IP address.
     *
     * @param inetAddress the string to validate
     * @return true if the string validates as an IP address
     */
    public static boolean isValidAddress(String inetAddress) {
        return isValidInet4Address(inetAddress) || isValidInet6Address(inetAddress);
    }

    /**
     * Validates an IPv4 address. Returns true if valid.
     *
     * @param inet4Address the IPv4 address to validate
     * @return true if the argument contains a valid IPv4 address
     */
    public static boolean isValidInet4Address(String inet4Address) {
        return InetAddressValidator.getInstance().isValidInet4Address(inet4Address);
    }

    /**
     * Validates an IPv6 address. Returns true if valid.
     *
     * @param inet6Address the IPv6 address to validate
     * @return true if the argument contains a valid IPv6 address
     * @since 1.4.1
     */
    public static boolean isValidInet6Address(String inet6Address) {
        return InetAddressValidator.getInstance().isValidInet6Address(inet6Address);
    }

    /**
     * Returns an integer representing an IPv4 address regardless of whether the supplied argument is an IPv4 address or not.
     *
     * @param inet4Address the IPv4 address to validate
     * @return an integer representing an IPv4 address
     */
    public static long inet4AddressToLong(String inet4Address) {
        return InetAddresses.coerceToInteger(InetAddresses.forString(inet4Address));
    }

    /**
     * Returns an Inet4Address having the integer value specified by the argument.
     *
     * @param address the 64bit long address to be converted
     * @return Inet4Address equivalent of the argument
     */
    public static String longToInet4Address(long address) {
        return InetAddresses.fromInteger(Math.toIntExact(address)).getHostAddress();
    }

    /**
     * 检测IP端口是否可以访问
     */
    public static boolean checkIpPort(String ip, int port) {
        boolean isReachable = false;
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), 1000);
            isReachable = true;
        } catch (Exception e) {
            // DO NOTHING
        }
        return isReachable;
    }

    /**
     * 检测IP地址是否可以访问
     */
    public static boolean checkIp(String ip) {
        boolean isReachable = false;
        try {
            isReachable = InetAddress.getByName(ip).isReachable(1000);
        } catch (IOException e) {
            // DO NOTHING
        }
        return isReachable;
    }

    public static SubnetUtils.SubnetInfo getSubnetInfo(final String cidrNotation) {
        return new SubnetUtils(cidrNotation).getInfo();
    }

    public static SubnetUtils.SubnetInfo getSubnetInfo(final String address, final String mask) {
        return new SubnetUtils(address, mask).getInfo();
    }
}
