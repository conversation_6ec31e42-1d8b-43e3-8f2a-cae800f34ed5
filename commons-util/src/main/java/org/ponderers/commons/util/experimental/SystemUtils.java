package org.ponderers.commons.util.experimental;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SystemUtils {

    private SystemUtils() {
    }

    /**
     * Get the current process ID
     *
     * @return process ID
     */
    public static Optional<Long> getProcessId() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        Pattern pattern = Pattern.compile("^(\\d+)@.+$", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(runtimeMXBean.getName());
        if (matcher.matches()) {
            long pid = Long.parseLong(matcher.group(1));
            return Optional.of(pid);
        }
        return Optional.empty();
    }


}
