package org.ponderers.commons.util.security;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;

public final class XssUtils {

    private static final Safelist safelist = Safelist.basicWithImages();

    private XssUtils() {
    }

    private static final Document.OutputSettings outputSettings = new Document.OutputSettings().prettyPrint(false);

    static {
        safelist.addAttributes(":all", "style");
    }

    public static String clean(String content) {
        return Jsoup.clean(content, "", safelist, outputSettings);
    }
}
