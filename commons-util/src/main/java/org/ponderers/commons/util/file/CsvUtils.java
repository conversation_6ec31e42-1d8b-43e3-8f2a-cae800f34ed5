package org.ponderers.commons.util.file;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public class CsvUtils {

    private CsvUtils() {
    }

    /**
     * Split csv with commas outside quotes
     * <p>
     * <a href="https://stackoverflow.com/questions/6542996/how-to-split-csv-whose-columns-may-contain">how-to-split-csv-whose-columns-may-contain</a>
     * </p>
     */
    public static String[] parseColumns(String line, String separatorChar, boolean outsideQuotes) {
        String regex = outsideQuotes ? separatorChar.concat("(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)") : separatorChar;
        String[] columns = line.split(regex, -1);
        columns = Arrays.stream(columns)
                .map(column -> StringUtils.remove(column, "\t"))
                .map(column -> StringUtils.remove(column, "\""))
                .map(StringUtils::trim)
                .toArray(String[]::new);
        return columns;
    }

    public static String[] parseColumns(String line, String separatorChar) {
        return parseColumns(line, separatorChar, true);
    }
}
