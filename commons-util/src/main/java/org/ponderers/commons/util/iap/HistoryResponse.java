package org.ponderers.commons.util.iap;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoryResponse {
    @JsonProperty("signedTransactions")
    private List<String> signedTransactions;
    @JsonProperty("environment")
    private String environment;
    @JsonProperty("bundleId")
    private String bundleId;
    @JsonProperty("hasMore")
    private boolean hasMore;
    @JsonProperty("appAppleId")
    private int appAppleId;
    @JsonProperty("revision")
    private String revision;
}