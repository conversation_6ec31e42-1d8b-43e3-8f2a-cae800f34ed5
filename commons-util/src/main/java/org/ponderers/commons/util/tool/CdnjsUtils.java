package org.ponderers.commons.util.tool;

import lombok.Data;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.http.impl.client.CloseableHttpClient;
import org.ponderers.commons.util.http.ApacheHttpUtils;
import org.ponderers.commons.util.http.HttpRequest;
import org.ponderers.commons.util.json.JsonUtils;
import org.ponderers.commons.util.thread.ExecutorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.*;

/**
 * CdnJS工具类
 * <p>
 * 用于下载https://cdnjs.com/静态资源
 *
 * <AUTHOR>
 */
public final class CdnjsUtils {

    private static final Logger log = LoggerFactory.getLogger(CdnjsUtils.class);

    public static final String DEFAULT_STORAGE_BASE_PATH;

    static {
        DEFAULT_STORAGE_BASE_PATH = Paths.get(SystemUtils.getJavaIoTmpDir().getPath(), "cdnjs").toString();
    }

    private CdnjsUtils() {
    }

    /**
     * 抓取CDNJS前端静态库资源
     * <p>
     * CdnjsUtils.fetchCdnJSResource("jquery", "2.2.3");
     *
     * @param libraryName    库名称，例如：jquery
     * @param libraryVersion 库版本，例如：2.2.3
     */
    public static boolean fetchCdnJSResource(String libraryName, String libraryVersion) {
        return CdnjsUtils.fetchCdnJSResource(libraryName, libraryVersion, null);
    }

    /**
     * 抓取CDNJS前端静态库资源
     * <p>
     * CdnjsUtils.fetchCdnJSResource("jquery", "2.2.3", System.getProperty("java.io.tmpdir"));
     *
     * @param libraryName     库名称，例如：jquery
     * @param libraryVersion  库版本，例如：2.2.3
     * @param storageRootPath 存储根目录，例如：/tmp/
     */
    public static boolean fetchCdnJSResource(String libraryName, String libraryVersion, String storageRootPath) {
        final ExecutorService executor = ExecutorUtils.createPool("FetchCdnJS");
        try (CloseableHttpClient httpClient = ApacheHttpUtils.createClient()) {
            // 设置CDNJS资源存储根目录
            storageRootPath = StringUtils.defaultString(storageRootPath, DEFAULT_STORAGE_BASE_PATH);
            log.info("CDNJS STORAGE PATH: {}", storageRootPath);

            // 请求CdnJS资源列表
            String url = String.format("https://api.cdnjs.com/libraries/%s/", libraryName);
            Map<String, String> params = new HashMap<>();
            params.put("fields", "name,version,assets");
            HttpRequest httpRequest = HttpRequest.builder().url(url).queryParameters(params).build();
            Optional<String> optionalContent = ApacheHttpUtils.syncGet(httpClient, httpRequest);
            String content = optionalContent.orElseThrow(() -> new ContextedRuntimeException("请求CdnJS资源列表失败"));
            CdnJS cdnJS = JsonUtils.parseObject(content, CdnJS.class);

            // 多线程下载库资源
            CompletionService<Integer> completeService = new ExecutorCompletionService<>(executor);
            for (Asset asset : cdnJS.getAssets()) {
                if (libraryVersion.equals(asset.getVersion())) {
                    // 创建下载任务后多线程并发执行任务
                    List<JobTask> taskList = new ArrayList<>();
                    for (String libraryPath : asset.getFiles()) {
                        // 前端静态资源获取路径[https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap-theme.css]
                        String requestUrl = String.format("http://cdnjs.cloudflare.com/ajax/libs/%s/%s/%s", libraryName, libraryVersion, libraryPath);
                        // 前端静态资源存放路径[C:/libs/twitter-bootstrap/3.3.7/css/bootstrap-theme.css]
                        Path storagePath = Paths.get(storageRootPath, libraryName, libraryVersion, libraryPath);
                        JobTask jobTask = new JobTask(httpClient, requestUrl, storagePath);
                        taskList.add(jobTask);
                        completeService.submit(jobTask);
                    }
                    // 统计资源占用存储空间与下载资源耗时
                    Long totalSize = 0L;
                    for (int i = 0; i < taskList.size(); i++) {
                        totalSize += completeService.take().get();
                    }
                    log.info("下载前端资源文件完毕, libraryName: {}, libraryVersion: {}, 文件总大小：{}",
                            libraryName, libraryVersion, FileUtils.byteCountToDisplaySize(totalSize));
                }
            }
        } catch (ExecutionException | IOException e) {
            throw new ContextedRuntimeException("下载前端资源文件出错", e)
                    .addContextValue("libraryName", libraryName)
                    .addContextValue("libraryVersion", libraryVersion);
        } catch (InterruptedException e) {
            log.warn("Interrupted!", e);
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }
        return true;
    }

    /**
     * 下载任务
     */
    @Data
    private static final class JobTask implements Callable<Integer> {

        private CloseableHttpClient httpClient;
        private String requestUrl;
        private Path storagePath;

        public JobTask(CloseableHttpClient httpClient, String requestUrl, Path storagePath) {
            this.httpClient = httpClient;
            this.requestUrl = requestUrl;
            this.storagePath = storagePath;
        }

        @Override
        public Integer call() throws Exception {
            log.info("Downloading assets, requestUrl: {}, storagePath: {}", requestUrl, storagePath);
            int fileSize = 0;
            try (InputStream in = ApacheHttpUtils.httpGetSteam(httpClient, requestUrl)) {
                if (Objects.isNull(in) || in.available() <= 0) {
                    return fileSize;
                }
                fileSize = in.available();
                Path parentStoragePath = storagePath.getParent();
                if (parentStoragePath == null) {
                    throw new ContextedRuntimeException("获取存储路径的父路径失败")
                            .addContextValue("requestUrl", requestUrl)
                            .addContextValue("storagePath", storagePath.toString());
                }
                FileUtils.forceMkdir(storagePath.toFile());
                Files.copy(in, storagePath, StandardCopyOption.REPLACE_EXISTING);
            }
            return fileSize;
        }
    }

    @Data
    private static class CdnJS {
        private String name;
        private String version;
        private List<Asset> assets;
    }

    @Data
    private static class Asset {
        private String version;
        private List<String> files;
    }

}
