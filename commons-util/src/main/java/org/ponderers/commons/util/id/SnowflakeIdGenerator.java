package org.ponderers.commons.util.id;

import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * Twitter Snowflake
 *
 * <AUTHOR>
 */

public class SnowflakeIdGenerator implements IdGenerator {

    private static final Logger log = LoggerFactory.getLogger(SnowflakeIdGenerator.class);

    private long workerIdBits = 5L;
    private long dataCenterIdBits = 5L;
    private long sequenceBits = 12L;
    private long workerIdShift = sequenceBits;
    private long dataCenterIdShift = sequenceBits + workerIdBits;
    private long timestampLeftShift = sequenceBits + workerIdBits + dataCenterIdBits;
    private long sequenceMask = ~(-1L << sequenceBits);

    private long lastTimestamp = -1L;

    private long sequence = 0L;

    private long workerId;
    private long dataCenterId;

    public SnowflakeIdGenerator(long dataCenterId, long workerId) {
        long maxWorkerId = ~(-1L << workerIdBits);
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
        }
        long maxDataCenterId = ~(-1L << dataCenterIdBits);
        if (dataCenterId > maxDataCenterId || dataCenterId < 0) {
            throw new IllegalArgumentException(String.format("dataCenter Id can't be greater than %d or less than 0", maxDataCenterId));
        }
        log.info("worker starting. timestamp left shift {}, dataCenter id bits {}, worker id bits {}, sequence bits {}, workerId {}",
                timestampLeftShift, dataCenterIdBits, workerIdBits, sequenceBits, workerId);
        this.workerId = workerId;
        this.dataCenterId = dataCenterId;
    }

    @SneakyThrows
    public synchronized long nextId() {
        long currentTimestamp = System.currentTimeMillis();
        // 当检测到系统当前时间小于上次ID生成时的时间（即回拨）
        if (currentTimestamp < lastTimestamp) {
            long offset = lastTimestamp - currentTimestamp;
            if (offset <= 5) {
                // 小幅回拨（如几毫秒）：阻塞等待，直到系统时间追赶上上次生成ID的时间。
                Thread.sleep(offset);
                currentTimestamp = System.currentTimeMillis();
                if (currentTimestamp < lastTimestamp) {
                    log.error("Clock moved backwards and hasn't recovered.");
                    throw new InvalidSystemClockException("Clock moved backwards and hasn't recovered.");
                }
            }
            // 大幅回拨：拒绝生成，抛出异常以防止ID重复或乱序。
            log.error("clock is moving backwards. Rejecting requests until {}.", lastTimestamp);
            throw new InvalidSystemClockException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - currentTimestamp));
        }

        if (lastTimestamp == currentTimestamp) {
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                currentTimestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = currentTimestamp;
        long twepoch = 1288834974657L;
        return ((currentTimestamp - twepoch) << timestampLeftShift) |
                (dataCenterId << dataCenterIdShift) |
                (workerId << workerIdShift) |
                sequence;
    }

    private long tilNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        return timestamp;
    }

    public static class InvalidSystemClockException extends RuntimeException {
        public InvalidSystemClockException(String message) {
            super(message);
        }
    }
}
