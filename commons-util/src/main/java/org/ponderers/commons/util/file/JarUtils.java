package org.ponderers.commons.util.file;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.Optional;
import java.util.jar.Attributes;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

public final class JarUtils {

    private static final Logger log = LoggerFactory.getLogger(JarUtils.class);

    private JarUtils() {
    }

    /**
     * 获取jar包中的MANIFEST.MF文件指定属性值
     *
     * @param jar  Jar文件
     * @param name 属性名称
     * @return MainAttribute属性
     */
    public static Optional<String> getMainAttribute(File jar, String name) {
        if (jar == null || !jar.exists()) {
            return Optional.empty();
        }
        try (JarFile jarFile = new JarFile(jar)) {
            Manifest manifest = jarFile.getManifest();
            Attributes attributes = manifest.getMainAttributes();
            return Optional.ofNullable(attributes.getValue(name));
        } catch (IOException e) {
            log.warn("获取jar包中的Manifest文件指定属性值{}失败", name);
        }
        return Optional.empty();
    }

}

