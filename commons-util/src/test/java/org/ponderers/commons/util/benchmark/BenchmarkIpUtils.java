package org.ponderers.commons.util.benchmark;

import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.ponderers.commons.util.net.IpUtils;

import java.util.concurrent.TimeUnit;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
@Fork(value = 2, jvmArgs = {"-Xms300m", "-Xmx1G"})
@Warmup(iterations = 3, time = 1)
@Measurement(iterations = 5, time = 1)
public class BenchmarkIpUtils {

    @Benchmark
    public void getServerIpAddress1(Blackhole bh) {
        String serverIpAddress = IpUtils.getServerIpAddress();
        bh.consume(serverIpAddress);
    }

    @Benchmark
    public void getServerIpAddress2(Blackhole bh) {
        String serverIpAddress = IpUtils.getServerIpAddress("192.", "172.", "10.");
        bh.consume(serverIpAddress);
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(BenchmarkIpUtils.class.getSimpleName())
                .forks(1)
                .build();
        new Runner(opt).run();
    }
}
