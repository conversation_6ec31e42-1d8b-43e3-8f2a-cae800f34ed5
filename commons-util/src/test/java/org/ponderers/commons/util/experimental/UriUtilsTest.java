package org.ponderers.commons.util.experimental;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class UriUtilsTest {

    @Test
    void appendUri() {
        Assertions.assertEquals("https://example.com?name=<PERSON>", UriUtils.appendUri("https://example.com", "name=<PERSON>"));
        Assertions.assertEquals("https://example.com?name=John#fragment", UriUtils.appendUri("https://example.com#fragment", "name=<PERSON>"));
        Assertions.assertEquals("https://example.com?email=<EMAIL>&name=<PERSON>", UriUtils.appendUri("https://example.com?email=<EMAIL>", "name=<PERSON>"));
        Assertions.assertEquals("https://example.com?email=<EMAIL>&name=John#fragment", UriUtils.appendUri("https://example.com?email=<EMAIL>#fragment", "name=<PERSON>"));
        Assertions.assertEquals("https://example.com?name=<PERSON>", UriUtils.appendUri("https://example.com", "name", "<PERSON>"));
        Assertions.assertEquals("https://example.com?name=John#fragment", UriUtils.appendUri("https://example.com#fragment", "name", "John"));
        Assertions.assertEquals("https://example.com?email=john.doe%40email.com&name=John", UriUtils.appendUri("https://example.com?email=<EMAIL>", "name", "John"));
        Assertions.assertEquals("https://example.com?email=john.doe%40email.com&name=John#fragment", UriUtils.appendUri("https://example.com?email=<EMAIL>#fragment", "name", "John"));
    }
}