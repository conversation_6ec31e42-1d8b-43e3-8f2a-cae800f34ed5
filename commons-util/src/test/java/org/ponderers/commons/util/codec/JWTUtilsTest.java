package org.ponderers.commons.util.codec;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.ponderers.commons.util.crypto.JWTUtils;

@Slf4j
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class JWTUtilsTest {

    private static final String JWT_SECRET = "FFeI81EOSDhStRlMOPA2TnCRZX_hHDQy";
    private static final String JWT_ISSUER = "http://www.ponderers.cn";

    @Test
    void token() {
        long userId = 10086;
        String subject = String.valueOf(userId);
        String token = JWTUtils.createToken(subject, JWT_SECRET, JWT_ISSUER);
        Assertions.assertEquals(subject, JWTUtils.parseToken(token, JWT_SECRET, JWT_ISSUER));
    }
}