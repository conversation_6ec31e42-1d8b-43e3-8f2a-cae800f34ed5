package org.ponderers.commons.util.time;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DateHelperTest {

    @Test
    void isSameMonth() {
        Date date = DateHelper.parseDate("2019-10-01 00:00:00");
        Assertions.assertTrue(DateHelper.isSameMonth(date, DateUtils.addSeconds(date, 1)));
        Assertions.assertFalse(DateHelper.isSameMonth(date, DateUtils.addSeconds(date, -1)));
    }

    @Test
    void currentSeconds() {
        long seconds = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        Assertions.assertEquals(seconds, DateHelper.currentSeconds());
    }

    @Test
    void atStartEndOf() {
        Date date = DateHelper.parseDate("2020-04-04 12:23:30");
        Assertions.assertNotNull(date);
        Assertions.assertEquals("2020-04-04 00:00:00", DateHelper.format(DateHelper.atStartOfDay(date)));
        Assertions.assertEquals("2020-04-04 23:59:59", DateHelper.format(DateHelper.atEndOfDay(date)));
        Assertions.assertEquals("2020-03-30 00:00:00", DateHelper.format(DateHelper.atStartOfWeek(date)));
        Assertions.assertEquals("2020-04-05 23:59:59", DateHelper.format(DateHelper.atEndOfWeek(date)));
        Assertions.assertEquals("2020-04-01 00:00:00", DateHelper.format(DateHelper.atStartOfMonth(date)));
        Assertions.assertEquals("2020-04-30 23:59:59", DateHelper.format(DateHelper.atEndOfMonth(date)));
    }

    @Test
    void format() {
        Date date = DateHelper.parseDate("2020-04-04 12:23:30");
        Assertions.assertEquals("2020-04-04 12:23:30", DateHelper.format(date));
        Assertions.assertEquals("2020-04-04 12:23:30", DateHelper.format(date.getTime()));
        Assertions.assertEquals("2020", DateHelper.format(date.getTime(), "yyyy"));
        Assertions.assertEquals("04", DateHelper.format(date, "MM"));
        Assertions.assertEquals("00:00:00", DateHelper.format(DateHelper.toLocalDate(date), "HH:mm:ss"));
        Assertions.assertEquals("12:23:30", DateHelper.format(DateHelper.toLocalDateTime(date), "HH:mm:ss"));
    }

    @Test
    void humanReadableFormat() {
        Duration duration = Duration.ofHours(3);
        String durationFormat = DateHelper.humanReadableFormat(duration);
        log.warn("durationFormat: {}", durationFormat);
        Assertions.assertEquals("3h", durationFormat);
    }

    @Test
    void humanReadableFormatMills() {
        long mills = Duration.ofHours(3).toMillis();
        String durationFormat = DateHelper.humanReadableFormat(mills);
        log.warn("durationFormat: {}", durationFormat);
        Assertions.assertEquals("3h", durationFormat);
    }

    @Test
    void getDuration() {
        Date startDate = DateHelper.parseDate("2020-04-04 12:23:30");
        for (int i=0; i<=5; i++) {
            Date endDate = DateUtils.addDays(startDate, i);
            Duration duration = DateHelper.getDuration(startDate, endDate);
            Assertions.assertEquals(i, duration.toDays());
            log.warn("startDate: {}, endDate: {}, durationToDays: {}",
                    DateHelper.format(startDate), DateHelper.format(endDate), duration.toDays());
        }
    }

    @Test
    void formatYearMonth() {
        Date birthDay = DateHelper.parseDate("2020-04-04 21:45:00");
        Assertions.assertEquals("202004", DateHelper.formatYearMonth(birthDay));
    }

    @Test
    void isCrossMonth() {
        Date date = DateHelper.parseDate("2020-01-31 23:59:00");
        IntStream.rangeClosed(1, 59).forEachOrdered(amount -> {
            Date tmp = DateUtils.addSeconds(date, amount);
            boolean isCrossMonth = DateHelper.isCrossMonth(tmp, 1);
            log.warn("tmp: {}, isCrossMonth: {}", DateHelper.format(tmp), isCrossMonth);
            Assertions.assertFalse(isCrossMonth);
        });
        IntStream.rangeClosed(60, 119).forEachOrdered(amount -> {
            Date tmp = DateUtils.addSeconds(date, amount);
            boolean isCrossMonth = DateHelper.isCrossMonth(tmp, 1);
            log.warn("tmp: {}, isCrossMonth: {}", DateHelper.format(tmp), isCrossMonth);
            Assertions.assertTrue(isCrossMonth);
        });
        IntStream.rangeClosed(120, 180).forEachOrdered(amount -> {
            Date tmp = DateUtils.addSeconds(date, amount);
            boolean isCrossMonth = DateHelper.isCrossMonth(tmp, 1);
            log.warn("tmp: {}, isCrossMonth: {}", DateHelper.format(tmp), isCrossMonth);
            Assertions.assertFalse(isCrossMonth);
        });
    }

    @Test
    void toLocalDate() {
        Date currentDate = new Date();
        LocalDate localDate = DateHelper.toLocalDate(currentDate);
        log.warn("localDate: {}", localDate);
        Assertions.assertEquals(DateHelper.format(localDate), DateHelper.format(DateHelper.atStartOfDay(currentDate)));
    }

    @Test
    void toLocalDateTime() {
        Date currentDate = new Date();
        LocalDateTime localDateTime = DateHelper.toLocalDateTime(currentDate);
        log.warn("localDateTime: {}, currentDate: {}", localDateTime, currentDate);
        Assertions.assertEquals(DateHelper.format(localDateTime), DateHelper.format(currentDate));
    }

    @Test
    void toLocalDateTimeMills() {
        long currentTimeMillis = System.currentTimeMillis();
        LocalDateTime localDateTime = DateHelper.toLocalDateTime(System.currentTimeMillis());
        log.warn("localDateTime: {}", localDateTime);
        Assertions.assertEquals(currentTimeMillis, localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    @Test
    void toDate() {
        LocalDate localDate = DateHelper.toLocalDate(new Date());
        Date date = DateHelper.toDate(localDate);
        log.warn("localDate: {}, date: {}", DateHelper.format(localDate), DateHelper.format(date));
        Assertions.assertEquals(DateHelper.format(localDate), DateHelper.format(date));
    }

    @Test
    void toDateMills() {
        LocalDateTime localDateTime = DateHelper.toLocalDateTime(System.currentTimeMillis());
        Date date = DateHelper.toDate(localDateTime);
        log.warn("localDateTime: {}, date: {}", DateHelper.format(localDateTime), DateHelper.format(date));
        Assertions.assertEquals(DateHelper.format(date), DateHelper.format(localDateTime));
    }

    @Test
    void duration() {
        Date current = new Date();
        LocalDateTime date1 = DateHelper.toLocalDateTime(current);
        LocalDateTime date2 = DateHelper.toLocalDateTime(DateUtils.addDays(current, 10));
        Duration duration = Duration.between(date1, date2);
        log.warn("duration: {}", duration.toDays());
        Assertions.assertEquals(10, duration.toDays());
    }

    @Test
    void getDatesBetween() {
        Date startDate = DateHelper.parseDate("2023-08-27 00:00:00");
        Date endDate = DateHelper.parseDate("2023-09-02 00:00:00");
        List<Date> dateList = DateHelper.getDatesBetween(startDate, endDate);
        Assertions.assertEquals(6, dateList.size());
        dateList.forEach(date -> System.out.println(DateHelper.format(date)));
    }

    @Test
    void getWeekOfYear() {
        Assertions.assertEquals(1, DateHelper.getWeekOfYear(DateHelper.parseDate("2023-01-01 00:00:00")));
        Assertions.assertEquals(53, DateHelper.getWeekOfYear(DateHelper.parseDate("2023-12-31 23:59:59")));
        Assertions.assertEquals(1, DateHelper.getWeekOfYear(DateHelper.parseDate("2024-01-01 00:00:00")));
        Assertions.assertEquals(53, DateHelper.getWeekOfYear(DateHelper.parseDate("2024-12-31 23:59:59")));
    }

    /**
     * 证明 localDate.get(WeekFields.ISO.weekOfYear()) 可能会导致同一自然周被分割到不同的周数。
     * 例如，2020年12月28日（周一）到2021年1月3日（周日）这一周：
     * - ISO标准会将这一周分割：2020-12-28到2020-12-31属于第53周，2021-01-01到2021-01-03属于第0周
     * - 我们的DateHelper.getWeekOfYear()修复了这个问题，确保同一自然周的所有日期都返回相同的周数
     */
    @Test
    void getWeekOfYear2() {
        // ISO标准的问题：同一自然周被分割到不同周数
        Assertions.assertEquals(53, DateHelper.toLocalDate(DateHelper.parseDate("2020-12-28 12:00:00")).get(WeekFields.ISO.weekOfYear()));
        Assertions.assertEquals(0, DateHelper.toLocalDate(DateHelper.parseDate("2021-01-03 12:00:00")).get(WeekFields.ISO.weekOfYear()));

        // 我们的修复：同一自然周返回相同的周数
        Assertions.assertEquals(53, DateHelper.getWeekOfYear(DateHelper.parseDate("2020-12-28 12:00:00")));
        Assertions.assertEquals(53, DateHelper.getWeekOfYear(DateHelper.parseDate("2021-01-03 12:00:00")));
    }

    @Test
    void getQuarterOfYear() {
        Assertions.assertEquals(1, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-01-01 00:00:00")));
        Assertions.assertEquals(1, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-03-31 23:59:59")));
        Assertions.assertEquals(2, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-04-01 00:00:00")));
        Assertions.assertEquals(2, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-06-30 23:59:59")));
        Assertions.assertEquals(3, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-07-01 00:00:00")));
        Assertions.assertEquals(3, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-09-30 23:59:59")));
        Assertions.assertEquals(4, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-10-01 00:00:00")));
        Assertions.assertEquals(4, DateHelper.getQuarterOfYear(DateHelper.parseDate("2024-12-31 23:59:59")));
    }
}