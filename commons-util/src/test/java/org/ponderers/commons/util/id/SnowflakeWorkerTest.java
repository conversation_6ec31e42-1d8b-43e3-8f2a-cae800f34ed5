package org.ponderers.commons.util.id;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.net.IpUtils;

import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

@Slf4j
class SnowflakeWorkerTest {

    @Test
    @Disabled
    @SneakyThrows
    void getWorkerId() {
        SnowflakeWorker snowflakeWorker = new SnowflakeWorker(
                IpUtils.getServerIpAddress(), 9999, "***********:2181");
        int workerId = snowflakeWorker.getWorkerId();
        log.warn("workerId: {}", workerId);
        Assertions.assertTrue(workerId >= 0);
        TimeUnit.SECONDS.sleep(7);
    }

    @Test
    void fromZnodeName() {
        String znodeName = "**********:8888-0000000039";
        SnowflakeWorker.WorkerNode workerNode = SnowflakeWorker.WorkerNode.fromZnodeName(znodeName);
        log.info("workerNode: {}", workerNode);
        Assertions.assertEquals(7, workerNode.getWorkerId());
        Assertions.assertEquals(39, workerNode.getSequence());
        Assertions.assertEquals("**********:8888", workerNode.getIpPort());
        Assertions.assertEquals(znodeName, workerNode.getZnodeName());
        Assertions.assertEquals("/snowflake/worker/forever/" + znodeName, workerNode.getZnodePath());
    }
}