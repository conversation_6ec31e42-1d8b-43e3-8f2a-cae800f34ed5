package org.ponderers.commons.util.benchmark;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.MapType;
import lombok.SneakyThrows;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@BenchmarkMode({Mode.Throughput})
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Thread)
@Warmup(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
@Fork(1)
@Threads(10)
public class BenchmarkJackson {
    String json = "{ \"color\" : \"Black\", \"type\" : \"BMW\" }";

    @State(Scope.Benchmark)
    public static class BenchmarkState {
        ObjectMapper singletonMapper = new ObjectMapper();
        ThreadLocal<ObjectMapper> threadLocalMapper = new ThreadLocal<>();
    }

    @SneakyThrows
    public static <K, V> Map<K, V> parseMap(ObjectMapper mapper, String content, Class<K> keyClass, Class<V> valueClass) {
        MapType mapType = mapper.getTypeFactory().constructMapType(HashMap.class, keyClass, valueClass);
        return mapper.readValue(content, mapType);
    }

    @Benchmark
    public Map<String, String> singleton(BenchmarkState state) {
        return parseMap(state.singletonMapper, json, String.class, String.class);
    }

    @Benchmark
    public Map<String, String> threadLocal(BenchmarkState state) {
        if (state.threadLocalMapper.get() == null) {
            state.threadLocalMapper.set(new ObjectMapper());
        }
        return parseMap(state.threadLocalMapper.get(), json, String.class, String.class);
    }

    @Benchmark
    public Map<String, String> prototype() {
        ObjectMapper prototypeMapper = new ObjectMapper();
        return parseMap(prototypeMapper, json, String.class, String.class);
    }

    public static void main(String[] args) throws Exception {
        new Runner(new OptionsBuilder()
                .include(BenchmarkJackson.class.getSimpleName())
                .forks(1)
                .build()).run();
    }
}
