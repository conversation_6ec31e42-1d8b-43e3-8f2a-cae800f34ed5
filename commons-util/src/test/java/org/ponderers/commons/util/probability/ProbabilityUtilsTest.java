package org.ponderers.commons.util.probability;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.ponderers.commons.util.experimental.ResourceUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ProbabilityUtilsTest {

    @Test
    void generateProbabilityEvents() {
        String json = ResourceUtils.resourceToString("probability.json");
        IntStream.rangeClosed(0, 2001).forEach(input -> {
            List<ProbabilityEvent> probabilityEventList = ProbabilityUtils.generateProbabilityEvents(json, input);
            BigDecimal sumRate = probabilityEventList.stream()
                    .map(ProbabilityEvent::getProbability)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            List<BigDecimal> probabilityList = probabilityEventList.stream().map(ProbabilityEvent::getProbability).collect(Collectors.toList());
            log.warn("input:{}, sumRate: {}, probabilityList: {}", input, sumRate.stripTrailingZeros(), probabilityList);
            Assertions.assertEquals(0, BigDecimal.ONE.compareTo(sumRate));
        });
    }

}