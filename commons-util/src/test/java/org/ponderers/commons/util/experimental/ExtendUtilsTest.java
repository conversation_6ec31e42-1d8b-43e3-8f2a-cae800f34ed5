package org.ponderers.commons.util.experimental;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.crypto.ExtendUtils;
import org.ponderers.commons.util.crypto.SignUtils;
import org.ponderers.commons.util.json.JsonUtils;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
class ExtendUtilsTest {

    private String signKey;
    private String extendMd5;
    private String extendSha1;
    private String extendSha256;
    private String extendHmacSha256;

    @BeforeEach
    void beforeEach() {
        this.signKey = RandomStringUtils.randomAlphanumeric(10).toUpperCase();
        Map<String, Object> callbackArgs = Maps.newHashMap();
        callbackArgs.put("amount", "0.02");
        callbackArgs.put("addTime", "1650802549");
        callbackArgs.put("version", "20170111");
        this.extendMd5 = ExtendUtils.createExtend(SignUtils.SignTypeEnum.MD5, signKey, callbackArgs);
        this.extendSha1 = ExtendUtils.createExtend(SignUtils.SignTypeEnum.SHA_1, signKey, callbackArgs);
        this.extendSha256 = ExtendUtils.createExtend(SignUtils.SignTypeEnum.SHA_256, signKey, callbackArgs);
        this.extendHmacSha256 = ExtendUtils.createExtend(SignUtils.SignTypeEnum.HMAC_SHA_256, signKey, callbackArgs);
    }

    @Test
    void getCallbackArgsMd5() {
        String version = ExtendUtils.getCallbackArgsByName(extendMd5, "version", String.class, "");
        Assertions.assertEquals("20170111", version);
        BigDecimal amount = ExtendUtils.getCallbackArgsByName(extendMd5, "amount", BigDecimal.class, null);
        Assertions.assertEquals(new BigDecimal("0.02"), amount);
        long addTime = ExtendUtils.getCallbackArgsByName(extendMd5, "addTime", Long.class, 0L);
        Assertions.assertEquals(1650802549L, addTime);
    }

    @Test
    void getCallbackArgsSha1() {
        String version = ExtendUtils.getCallbackArgsByName(extendSha1, "version", String.class, "");
        Assertions.assertEquals("20170111", version);
        BigDecimal amount = ExtendUtils.getCallbackArgsByName(extendSha1, "amount", BigDecimal.class, null);
        Assertions.assertEquals(new BigDecimal("0.02"), amount);
        long addTime = ExtendUtils.getCallbackArgsByName(extendSha1, "addTime", Long.class, 0L);
        Assertions.assertEquals(1650802549L, addTime);
    }

    @Test
    void getCallbackArgsSha256() {
        String version = ExtendUtils.getCallbackArgsByName(extendSha256, "version", String.class, "");
        Assertions.assertEquals("20170111", version);
        BigDecimal amount = ExtendUtils.getCallbackArgsByName(extendSha256, "amount", BigDecimal.class, null);
        Assertions.assertEquals(new BigDecimal("0.02"), amount);
        long addTime = ExtendUtils.getCallbackArgsByName(extendSha256, "addTime", Long.class, 0L);
        Assertions.assertEquals(1650802549L, addTime);
    }

    @Test
    void getCallbackArgsHmacSha256() {
        String version = ExtendUtils.getCallbackArgsByName(extendHmacSha256, "version", String.class, "");
        Assertions.assertEquals("20170111", version);
        BigDecimal amount = ExtendUtils.getCallbackArgsByName(extendHmacSha256, "amount", BigDecimal.class, null);
        Assertions.assertEquals(new BigDecimal("0.02"), amount);
        long addTime = ExtendUtils.getCallbackArgsByName(extendHmacSha256, "addTime", Long.class, 0L);
        Assertions.assertEquals(1650802549L, addTime);
    }

    @Test
    void getCallbackMetaMd5() {
        ExtendUtils.Extend.CallbackSign callbackSign = ExtendUtils.getCallbackSign(extendMd5);
        Assertions.assertEquals("MD5", callbackSign.getSignType());
    }

    @Test
    void getCallbackMetaSha1() {
        ExtendUtils.Extend.CallbackSign callbackSign = ExtendUtils.getCallbackSign(extendSha1);
        Assertions.assertEquals("SHA-1", callbackSign.getSignType());
    }

    @Test
    void getCallbackMetaSha256() {
        ExtendUtils.Extend.CallbackSign callbackSign = ExtendUtils.getCallbackSign(extendSha256);
        Assertions.assertEquals("SHA-256", callbackSign.getSignType());
    }

    @Test
    void getCallbackMetaHmacSha256() {
        ExtendUtils.Extend.CallbackSign callbackSign = ExtendUtils.getCallbackSign(extendHmacSha256);
        Assertions.assertEquals("HMAC_SHA_256", callbackSign.getSignType());
    }

    @Test
    void checkExtend() {
        Assertions.assertTrue(ExtendUtils.checkExtend(extendMd5, signKey));
        Assertions.assertTrue(ExtendUtils.checkExtend(extendSha1, signKey));
        Assertions.assertTrue(ExtendUtils.checkExtend(extendSha256, signKey));
        Assertions.assertTrue(ExtendUtils.checkExtend(extendHmacSha256, signKey));
    }

    @Test
    void decodeExtend() {
        ExtendUtils.Extend extend = ExtendUtils.parseExtend(extendMd5);
        String prettyJson = JsonUtils.toPrettyJSONString(extend);
        log.warn("Extend prettyJson: {}", prettyJson);
        Assertions.assertNotNull(extend);
    }
}