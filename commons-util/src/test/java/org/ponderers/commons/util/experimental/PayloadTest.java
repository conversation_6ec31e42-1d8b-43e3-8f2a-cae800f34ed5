package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.model.JsonResult;
import org.ponderers.commons.util.model.status.SystemResultCode;

import java.math.BigDecimal;

@Slf4j
class PayloadTest {
    @Test
    void builder() {
        Payload payload = Payload.builder().build();
        log.warn("payload: {}", payload);
        Assertions.assertTrue(payload.isEmpty());
    }

    @Test
    void map() {
        Payload payload = Payload.builder()
                .map("a", 1)
                .map("b", true)
                .map("c", BigDecimal.valueOf(12.45f))
                .build();
        Assertions.assertEquals(3, payload.size());
        Assertions.assertTrue(payload.containsKey("a"));
        Assertions.assertTrue(payload.containsKey("b"));
        Assertions.assertTrue(payload.containsKey("c"));
        log.warn("payload: {}", payload);
        JsonResult<Payload> jsonResult = JsonResult.result(SystemResultCode.SUCCESS, payload);
        log.warn("jsonResult: {}", jsonResult);
    }
}