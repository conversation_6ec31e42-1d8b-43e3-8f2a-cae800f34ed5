package org.ponderers.commons.util.codec;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.FixMethodOrder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runners.MethodSorters;
import org.ponderers.commons.util.BaseTest;
import org.ponderers.commons.util.crypto.TripleDESUtils;

import java.nio.charset.StandardCharsets;

/**
 * Triple DEC Utils Test
 *
 * <AUTHOR>
 */
@Slf4j
@FixMethodOrder(MethodSorters.JVM)
class DES3UtilsTest extends BaseTest {

    private String randomText;
    private byte[] secretKey;

    @BeforeEach
    public void before() {
        randomText = RandomStringUtils.randomAlphanumeric(10);
        secretKey = RandomStringUtils.randomAlphanumeric(24).getBytes(StandardCharsets.UTF_8);
    }

    @Test
    void enDecrypt() {
        String encryptText = TripleDESUtils.encrypt(randomText, secretKey);
        Assertions.assertTrue(StringUtils.isNoneBlank(encryptText));
        String plainText = TripleDESUtils.decrypt(encryptText, secretKey);
        Assertions.assertEquals(randomText, plainText);
        log.info("plain text: {}, encrypted text: {}, decrypted text: {}", randomText, encryptText, plainText);
    }
}