package org.ponderers.commons.util.experimental;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Optional;

@Slf4j
class ValidationUtilsTest {

    private CommonParam commonParam;

    @BeforeEach
    void before() {
        this.commonParam = CommonParam.builder()
                .userId(100000000L)
                .payChannel("Alipay")
                .amount(BigDecimal.valueOf(100))
                .build();
    }

    @Test
    void validate() {
        Optional<ConstraintViolation<CommonParam>> optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertFalse(optionalViolation.isPresent());
    }

    @Test
    void validateBigDecimal() {
        Optional<ConstraintViolation<CommonParam>> optionalViolation;
        commonParam.setAmount(null);
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
        commonParam.setAmount(BigDecimal.ZERO);
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
        commonParam.setAmount(BigDecimal.valueOf(-1));
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
    }

    @Test
    void validateString() {
        Optional<ConstraintViolation<CommonParam>> optionalViolation;
        commonParam.setPayChannel(null);
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
        commonParam.setPayChannel("123456");
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
    }

    @Test
    void validateLong() {
        Optional<ConstraintViolation<CommonParam>> optionalViolation;
        commonParam.setUserId(null);
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
        log.warn("message: {}", optionalViolation.get().getMessage());
        commonParam.setUserId(-1L);
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertTrue(optionalViolation.isPresent());
        log.warn("message: {}", optionalViolation.get().getMessage());
        commonParam.setUserId(66L);
        optionalViolation = ValidationUtils.validate(commonParam);
        log.warn("optionalViolation: {}", optionalViolation);
        Assertions.assertFalse(optionalViolation.isPresent());
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommonParam {
        @NotNull
        @DecimalMin(value = "0", inclusive = false)
        private BigDecimal amount;
        @NotNull
        @Pattern(regexp = "[a-zA-Z]*")
        private String payChannel;
        @NotNull(message = "userId参数不允许为空")
        @Range(min = 0, max = Integer.MAX_VALUE)
        private Long userId;
    }
}