package org.ponderers.commons.util.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;

@Data
public class GiftInfo {
    /**
     * 礼物ID
     */
    private int id;

    private int cornerMarkerType;

    private String cornerMarkerText;

    private String cornerMarkerText2;

    private String cornerMarkerColor1;

    private String cornerMarkerColor2;

    private String cornerMarkerImage;

    private String cornerMarkerTextColor;

    private String cornerColor1;
    private String cornerColor2;

    private String name;

    private String url;

    private String pic;

    private String price;

    private String exchange;

    private String category;

    private String mix;

    private String fly;

    private String className;

    private String sortIndex;

    private String status;

    private String image;

    private String isNew;

    private String imageTrans;

    private String imageGrade;

    private String expire;

    private String richLevelLimit;

    private String type;

    private String adtEffect;

    private String guardLevelLimit;

    private String vipLimit;

    private String isPile;

    private String canNotSend;

    private String extraType;

    private Object userIdLimit;

    private String userLimitNickName;

    private String starFansLimit;

    private String mobileImage;

    private String isPk;

    private String isFullShow;

    private String duration;

    private String lGuardLevelLimit;

    private String rateLimitType;

    private String rateLimitNum;

    private String rateLimitExpiry;

    private String isShow;

    private int topCount;

    private int specialType;

    private int guard;

    private String giftUrl;

    private int week;

    private String happyObj;

    private String happyType;

    private int isSuper;

    private String giftTips;

    private String videoUrl;

    private String isRollGift;

    private String rollNum;

    private String multiVideoUrls;

    private int giftEffectType;

    public int starVipLevel;

    private int gameType;

    private int isGlobal;

    private int isGlobalNotice;

    public String extAttr;

    @JSONField(name = "class")
    public String getClassName() {
        return className;
    }

    @JSONField(name = "super")
    public int getIsSuper() {
        return isSuper;
    }

    private int newTime;

    private int attrType = AttrTypeEnum.INIT.getValue();

    @Getter
    public enum AttrTypeEnum {

        INIT(0, "初始态（无意义）"),
        OPERATION_TYPE(1, "运营位礼物");

        private final int value;
        private final String desc;

        AttrTypeEnum(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
}
