package org.ponderers.commons.util.id;

import com.google.common.collect.Sets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Set;

class SnowflakeIdGeneratorTest {

    @Test
    void nextId() {
        try {
            final int expectedGeneratedNum = 500000;
            SnowflakeIdGenerator worker = new SnowflakeIdGenerator(1, 1);
            Set<Long> idSet = Sets.newHashSet();
            for (int i = 0; i < expectedGeneratedNum; i++) {
                idSet.add(worker.nextId());
            }
            Assertions.assertEquals(expectedGeneratedNum, idSet.size());
        } catch (Exception exception) {
            Assertions.assertTrue(exception instanceof SnowflakeIdGenerator.InvalidSystemClockException);
        }
    }
}