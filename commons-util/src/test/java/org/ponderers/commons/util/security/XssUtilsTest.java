package org.ponderers.commons.util.security;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@Slf4j
class XssUtilsTest {
    @Test
    void clean() {
        String unsafe = "<a href='https://bing.com' onclick='stealCookies()'>Bing</a>";
        String expect = "<a href=\"https://bing.com\" rel=\"nofollow\">Bing</a>";
        String safe = XssUtils.clean(unsafe);
        log.warn("unsafe: {}, safe: {}", unsafe, safe);
        Assertions.assertEquals(expect, safe);
    }

}