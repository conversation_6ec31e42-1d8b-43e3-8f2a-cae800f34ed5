package org.ponderers.commons.util.crypto;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.security.InvalidKeyException;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class AESUtilsTest {

    @Test
    void randomBytes() {
        byte[] key = AESUtils.randomBytes(16);
        Assertions.assertEquals(16, key.length);
    }

    @Test
    void decrypt() {
        final String plainText = "Hello, World!";
        byte[] key = AESUtils.randomBytes(16);
        log.warn(Hex.encodeHexString(key));
        byte[] cipherText = AESUtils.encrypt(plainText, key);
        String decryptedText = AESUtils.decrypt(cipherText, key);
        log.warn("Plain Text: {}", plainText);
        log.warn("Decrypted Text: {}", decryptedText);
        Assertions.assertEquals(plainText, decryptedText);
        Assertions.assertThrows(InvalidKeyException.class, () -> {
            AESUtils.encrypt(plainText, AESUtils.randomBytes(2));
        });
        Assertions.assertThrows(InvalidKeyException.class, () -> {
            AESUtils.decrypt(cipherText, AESUtils.randomBytes(2));
        });
    }
}