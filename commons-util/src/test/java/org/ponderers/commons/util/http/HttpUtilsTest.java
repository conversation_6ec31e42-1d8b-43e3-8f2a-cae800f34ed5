package org.ponderers.commons.util.http;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.http.impl.client.CloseableHttpClient;
import org.junit.jupiter.api.*;
import org.ponderers.commons.util.json.JsonUtils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Disabled
class HttpUtilsTest {

    private static final Proxy proxyClash = new Proxy(Proxy.Type.HTTP,
            new InetSocketAddress("127.0.0.1", 7890));

    private static final Proxy proxyWhistle = new Proxy(Proxy.Type.HTTP,
            new InetSocketAddress("127.0.0.1", 8899));

    private static final OkHttpClient okClient = OkHttpUtils.createClient();

    private static final OkHttpClient okClientClash = OkHttpUtils.createClient(
            1000, 3000, 3000, proxyClash);

    private static final OkHttpClient okClientWhistle = OkHttpUtils.createClient(
            1000, 3000, 3000, proxyWhistle);

    private static final CloseableHttpClient apacheClient = ApacheHttpUtils.createClient(
            1000, 3000, 3000);

    private static final org.apache.hc.client5.http.impl.classic.CloseableHttpClient apache5Client =
            ApacheHttp5Utils.createClient(1000, 3000, 3000);

    private static final CloseableHttpClient apacheClientClash = ApacheHttpUtils.createClient(
            1000, 3000, 3000, proxyClash);

    private static final CloseableHttpClient apacheClientWhistle = ApacheHttpUtils.createClient(
            1000, 3000, 3000, proxyWhistle);

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void syncGet() {
        String url = "http://totoro.ponderers.cn/management/actuator/health";
        HttpRequest httpRequest = HttpRequest.builder().url(url).build();
        Optional<String> optionalContent1 = OkHttpUtils.syncGet(okClient, httpRequest);
        log.warn("syncGet, optionalContent1: {}", optionalContent1);
        Optional<String> optionalContent2 = ApacheHttpUtils.syncGet(apacheClient, httpRequest);
        log.warn("syncGet, optionalContent2: {}", optionalContent2);
        Optional<String> optionalContent3 = ApacheHttp5Utils.syncGet(apache5Client, httpRequest);
        log.warn("syncGet, optionalContent3: {}", optionalContent3);
        Assertions.assertEquals(optionalContent1, optionalContent2);
        Assertions.assertEquals(optionalContent1, optionalContent3);
        Assertions.assertTrue(optionalContent1.isPresent());
        String status = JsonUtils.parseJsonPathChecked(optionalContent1.get(), "$.status", String.class);
        Assertions.assertEquals("UP", status);
    }

    @Test
    @Timeout(value = 2000, unit = TimeUnit.MILLISECONDS)
    void asyncGet() throws InterruptedException {
        String url = "http://totoro.ponderers.cn/management/actuator/health";
        HttpRequest httpRequest = HttpRequest.builder().url(url).build();
        OkHttpUtils.asyncGet(okClient, httpRequest, optionalContent -> {
            log.warn("optionalContent: {}", optionalContent);
            Assertions.assertTrue(optionalContent.isPresent());
            String status = JsonUtils.parseJsonPathChecked(optionalContent.get(), "$.status", String.class);
            Assertions.assertEquals("UP", status);
        });
        TimeUnit.SECONDS.sleep(1);
    }

    @Test
    void syncPostJsonBody() {
        String url = "http://totoro.ponderers.cn/management/api/v1/test/groovy";
        String jsonBody = """
                        {
                            "expression": "def query = ruleService.testQuery(a); query;",
                            "bindParams": {
                                "a": 2
                            }
                        }
                """;
        HttpRequest request = HttpRequest.builder().url(url).jsonBody(jsonBody).build();
        Optional<String> optionalContent1 = OkHttpUtils.syncPostJsonBody(okClient, request);
        log.warn("syncPostJsonBody, optionalContent1: {}", optionalContent1);
        Assertions.assertTrue(optionalContent1.isPresent());
        Optional<String> optionalContent2 = ApacheHttpUtils.syncPostJsonBody(apacheClient, request);
        log.warn("syncPostJsonBody, optionalContent2: {}", optionalContent2);
        Assertions.assertEquals(optionalContent1, optionalContent2);
        Optional<String> optionalContent3 = ApacheHttp5Utils.syncPostJsonBody(apache5Client, request);
        log.warn("syncPostJsonBody, optionalContent3: {}", optionalContent3);
        Assertions.assertEquals(optionalContent1, optionalContent3);
    }

    @Test
    void syncPostFormBody() {
        String url = "http://totoro.ponderers.cn/management/api/v1/test/postFormBody";
        Map<String, String> bodyParams = Maps.newHashMap();
        bodyParams.put("app_id", "1");
        bodyParams.put("app_name", "游戏");
        HttpRequest request = HttpRequest.builder().url(url).bodyParams(bodyParams).build();
        Optional<String> optionalContent1 = OkHttpUtils.syncPostFormBody(okClient, request);
        log.warn("syncPostFormBody, optionalContent1: {}", optionalContent1);
        Optional<String> optionalContent2 = ApacheHttpUtils.syncPostFormBody(apacheClient, request);
        log.warn("syncPostFormBody, optionalContent2: {}", optionalContent2);
        Assertions.assertEquals(optionalContent1, optionalContent2);
        Optional<String> optionalContent3 = ApacheHttp5Utils.syncPostFormBody(apache5Client, request);
        log.warn("syncPostFormBody, optionalContent3: {}", optionalContent3);
        Assertions.assertEquals(optionalContent1, optionalContent3);
        Assertions.assertTrue(optionalContent1.isPresent());
    }

}