package org.ponderers.commons.util.id;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.*;
import org.ponderers.commons.util.time.DateHelper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
class GlobalIdUtilsTest {

    private static final int GENERATED_ORDER_NUM = 100_0000;
    private static final int AVAILABLE_CORES = Runtime.getRuntime().availableProcessors();
    private Set<String> orderNoSet;
    private CountDownLatch latch;
    private ExecutorService executorService;

    @BeforeEach
    public void beforeTest() {
        orderNoSet = Collections.synchronizedSet(new HashSet<>());
        latch = new CountDownLatch(GENERATED_ORDER_NUM);
        executorService = Executors.newFixedThreadPool(AVAILABLE_CORES);
    }

    @AfterEach
    public void afterTest() {
        String firstOrderNo = StringUtils.EMPTY;
        for (String orderNo : orderNoSet) {
            if (StringUtils.isEmpty(firstOrderNo)) {
                firstOrderNo = orderNo;
                log.info(firstOrderNo);
            }
            if (orderNo.length() != firstOrderNo.length()) {
                Assertions.fail("生成的订单号长度不统一" + orderNo + "<>" + firstOrderNo);
            }
        }
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newOrderId() throws InterruptedException {
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                String orderNo = GlobalIdUtils.newOrderId();
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(),"预期并发生成不重复订单号");
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newUUID() throws InterruptedException {
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                String orderNo = GlobalIdUtils.newUUID();
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(), "预期并发生成不重复订单号");
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newSnowflakeId() throws InterruptedException {
        final SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(0, 0);
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                String orderNo = String.valueOf(GlobalIdUtils.newSnowflakeId(snowflakeIdGenerator));
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(), "预期并发生成不重复订单号");
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newConcurrentId() throws InterruptedException {
        final ConcurrentIdGenerator concurrentIdGenerator = new ConcurrentIdGenerator(1);
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                String orderNo = String.valueOf(GlobalIdUtils.newConcurrentId(concurrentIdGenerator));
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(), "预期并发生成不重复订单号");
    }

    @Test
    @SneakyThrows
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newOrderNo() {
        final SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(1, 1);
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                String orderNo = GlobalIdUtils.newOrderNo(snowflakeIdGenerator);
                Assertions.assertTrue(orderNo.length() <= 32, "订单号长度不允许超过30");
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(), "预期并发生成不重复订单号");
    }

    @Test
    void parseYearMonth() {
        final SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(1, 1);
        String orderNo = GlobalIdUtils.newOrderNo(snowflakeIdGenerator);
        Optional<String> optionalMonth = GlobalIdUtils.parseYearMonth(orderNo);
        Assertions.assertTrue(optionalMonth.isPresent());
        Assertions.assertEquals(DateHelper.formatYearMonth(new Date()), optionalMonth.get());
    }

    @Test
    @SneakyThrows
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newOrderNoBySnowflakeId() {
        final SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(1, 1);
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                long snowflakeId = GlobalIdUtils.newSnowflakeId(snowflakeIdGenerator);
                String orderNo = GlobalIdUtils.newOrderNo(snowflakeId);
                Assertions.assertTrue(orderNo.length() <= 32, "订单号长度不允许超过30");
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(), "预期并发生成不重复订单号");
    }

    @Test
    @SneakyThrows
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void newNanoId() {
        for (int i = 0; i < GENERATED_ORDER_NUM; i++) {
            executorService.execute(() -> {
                String orderNo = GlobalIdUtils.newNanoId();
                Assertions.assertTrue(orderNo.length() <= 32, "订单号长度不允许超过30");
                orderNoSet.add(orderNo);
                latch.countDown();
            });
        }
        executorService.shutdown();
        boolean reached = latch.await(10, TimeUnit.SECONDS);
        Assertions.assertTrue(reached);
        Assertions.assertEquals(GENERATED_ORDER_NUM, orderNoSet.size(), "预期并发生成不重复订单号");
    }

    @Test
    @SneakyThrows
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void parseOrderNo() {
        final SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(1, 1);
        String orderNo = String.valueOf(GlobalIdUtils.newOrderNo(snowflakeIdGenerator));
        String dayInfo = orderNo.substring(5, 13);
        log.warn("dayInfo: {}", dayInfo);
        Date date1 = DateUtils.parseDate(dayInfo, "yyyyMMdd");
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Assertions.assertEquals(localDate1.getYear(), localDate2.getYear());
        Assertions.assertEquals(localDate1.getMonthValue(), localDate2.getMonthValue());
        Assertions.assertEquals(localDate1.getDayOfMonth(), localDate2.getDayOfMonth());
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void longLengthRange() {
        String maxSignedLongStr = String.valueOf(Long.MAX_VALUE);
        Assertions.assertEquals("9223372036854775807", maxSignedLongStr, "最大有符号Long");
        Assertions.assertEquals(19, maxSignedLongStr.length());
        String minSignedLongStr = String.valueOf(Long.MIN_VALUE);
        Assertions.assertEquals("-9223372036854775808", minSignedLongStr, "最小有符号Long");
        Assertions.assertEquals(20, minSignedLongStr.length());
        BigDecimal maxUnSignedLongStr = BigDecimal.valueOf(Long.MAX_VALUE).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(1));
        Assertions.assertEquals( "18446744073709551615", maxUnSignedLongStr.toPlainString(), "最大无符号Long");
        Assertions.assertEquals(20, maxUnSignedLongStr.toPlainString().length());
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    void intLengthRange() {
        String maxSignedIntStr = String.valueOf(Integer.MAX_VALUE);
        Assertions.assertEquals("2147483647", maxSignedIntStr, "最大有符号Int");
        Assertions.assertEquals(10, maxSignedIntStr.length());
        String minSignedIntStr = String.valueOf(Integer.MIN_VALUE);
        Assertions.assertEquals("-2147483648", minSignedIntStr, "最小有符号Int");
        Assertions.assertEquals(11, minSignedIntStr.length());
        BigDecimal maxUnSignedIntStr = BigDecimal.valueOf(Integer.MAX_VALUE).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(1));
        Assertions.assertEquals( "4294967295", maxUnSignedIntStr.toPlainString(), "最大无符号Int");
        Assertions.assertEquals(10, maxUnSignedIntStr.toPlainString().length());
    }
}