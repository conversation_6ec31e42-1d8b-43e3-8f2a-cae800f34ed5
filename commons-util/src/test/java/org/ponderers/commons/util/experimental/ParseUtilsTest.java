package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringEscapeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.math.ParseUtils;

import java.math.BigDecimal;

@Slf4j
class ParseUtilsTest {

    @Test
    void tryParseInt() {
        Assertions.assertEquals(0, ParseUtils.tryParseInt("12.33", 0));
        Assertions.assertEquals(12, ParseUtils.tryParseInt("12", 0));
    }

    @Test
    void tryParseLong() {
        Assertions.assertEquals(0, ParseUtils.tryParseLong("12.33", 0));
        Assertions.assertEquals(12, ParseUtils.tryParseLong("12", 0));
    }

    @Test
    void tryParseBigDecimal() {
        Assertions.assertEquals(new BigDecimal("12.33"), ParseUtils.tryParseBigDecimal("12.33", BigDecimal.ZERO));
        Assertions.assertEquals(BigDecimal.valueOf(12), ParseUtils.tryParseBigDecimal("12", BigDecimal.ZERO));
    }

    @Test
    void escapeJson() {
        String escaped = StringEscapeUtils.escapeJson("{\"gearId\":1}");
        log.warn("escaped: {}", escaped);
        Assertions.assertEquals("{\\\"gearId\\\":1}", escaped);
    }

    @Test
    void intPairToLong() {
        long expected = 123456789012323334L;
        Pair<Integer, Integer> intPair = ParseUtils.longToIntPair(expected);
        long actual = ParseUtils.intPairToLong(intPair);
        log.warn("expected: {}, actual: {}", expected, actual);
        Assertions.assertEquals(expected, actual);
    }
}