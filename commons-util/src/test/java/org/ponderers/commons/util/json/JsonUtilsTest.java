package org.ponderers.commons.util.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.dataformat.toml.TomlMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.jayway.jsonpath.InvalidJsonException;
import com.jayway.jsonpath.TypeRef;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.experimental.ResourceUtils;
import org.ponderers.commons.util.model.GiftInfo;

import java.math.BigDecimal;
import java.net.URL;
import java.util.*;

@Slf4j
class JsonUtilsTest {

    private String xmlValidData;
    private String xmlInvalidData;
    private String jsonInvalidData;
    private String jsonObjectData;
    private String jsonArrayData;
    private String giftListJson;
    private String wechatPayJson;

    @BeforeEach
    public void beforeEach() {
        this.xmlValidData = ResourceUtils.resourceToString("xml_valid_data.xml");
        this.xmlInvalidData = ResourceUtils.resourceToString("xml_invalid_data.xml");
        this.jsonInvalidData = ResourceUtils.resourceToString("json_invalid_data.json");
        this.jsonObjectData = ResourceUtils.resourceToString("json_object_data.json");
        this.jsonArrayData = ResourceUtils.resourceToString("json_array_data.json");
        this.giftListJson = ResourceUtils.resourceToString("giftList.json");
        this.wechatPayJson = ResourceUtils.resourceToString("wechatPay.json");
    }

    @Test
    void parseJsonPathUnchecked() {
        BigDecimal price = JsonUtils.parseJsonPathChecked(jsonObjectData, "$.store.bicycle.price", BigDecimal.class);
        Assertions.assertEquals("19.95", price.stripTrailingZeros().toPlainString());
        Assertions.assertThrows(ContextedRuntimeException.class, () -> JsonUtils.parseJsonPathChecked(
                jsonObjectData, "$.store.bicycle.price.not.exists", BigDecimal.class));
        String gender0 = JsonUtils.parseJsonPathChecked(jsonArrayData, "$[0]['gender']", String.class);
        Assertions.assertEquals("male", gender0);
        Assertions.assertThrows(ContextedRuntimeException.class, () -> JsonUtils.parseJsonPathChecked(
                jsonArrayData, "$[1]['gender']", String.class));
    }

    @Test
    void parseJsonPath() {
        Assertions.assertThrows(InvalidJsonException.class, () -> JsonUtils.parseJsonPath(jsonInvalidData, "$.expensive", Integer.class));
        Assertions.assertEquals(10, JsonUtils.parseJsonPath(jsonObjectData, "$.expensive", Integer.class).orElse(0).intValue());
        Assertions.assertEquals("red", JsonUtils.parseJsonPath(jsonObjectData, "$.store.bicycle.color", String.class).orElse(""));
        Assertions.assertEquals("Nigel Rees", JsonUtils.parseJsonPath(jsonObjectData, "$.store.book[0].author", String.class).orElse(""));
        Assertions.assertEquals(BigDecimal.valueOf(22.99), JsonUtils.parseJsonPath(jsonObjectData, "$.store.book[3].price", BigDecimal.class).orElse(BigDecimal.ZERO));
        Assertions.assertEquals("male", JsonUtils.parseJsonPath(jsonArrayData, "$[0]['gender']", String.class, ""));
        Assertions.assertEquals("", JsonUtils.parseJsonPath(jsonArrayData, "$[1]['gender']", String.class, ""));
        Assertions.assertEquals(1682895383L, JsonUtils.parseJsonPath("[{\"maxTime\":1682895383}]", "$.[0].maxTime", Long.class, 0L));
        Assertions.assertEquals(-1, JsonUtils.parseJsonPath(jsonInvalidData, "$.code", Integer.class, -1));
        Assertions.assertEquals(-1, JsonUtils.parseJsonPath("[]", "$.code", Integer.class, -1));
        Assertions.assertEquals(-1, JsonUtils.parseJsonPath("{}", "$.code", Integer.class, -1));
        Assertions.assertEquals(-1, JsonUtils.parseJsonPath("null", "$.code", Integer.class, -1));
        Assertions.assertEquals(-1, JsonUtils.parseJsonPath("{\"code\": null}", "$.code", Integer.class, -1));
        Assertions.assertEquals(1, JsonUtils.parseJsonPath("{\"code\": 1}", "$.code", Integer.class, -1));
    }

    @Test
    void parseJsonPathAbnormal() {
        Assertions.assertFalse(JsonUtils.parseJsonPath(jsonObjectData, "$.notExistPath", String.class).isPresent());
        Assertions.assertFalse(JsonUtils.parseJsonPath(jsonObjectData, "$.store.book[100].author", String.class).isPresent());
    }

    @Test
    void parseJsonPathException() {
        Assertions.assertThrows(InvalidJsonException.class, () -> JsonUtils.parseJsonPath(jsonInvalidData, "$.expensive", Integer.class));
    }

    @Test
    void parseJsonPathList() {
        String content = "{\"goodsList\":[{\"goodsId\":2000000055,\"goodsNum\":1,\"goodsType\":21,\"goodsName\":\"玫瑰\",\"goodsExt\":\"\"}],\"concertId\":1}";
        List<GoodsConfig> goodsConfigList = JsonUtils.parseJsonPathToTypeRef(content, "$.goodsList", new TypeRef<>() {
        });
        Assertions.assertTrue(CollectionUtils.isNotEmpty(goodsConfigList));
        Assertions.assertEquals(1, goodsConfigList.size());
        GoodsConfig goodsConfig = goodsConfigList.stream().findFirst().orElseThrow(() -> new ContextedRuntimeException("unit test exception"));
        Assertions.assertEquals(21, goodsConfig.getGoodsType());
        Assertions.assertEquals(2000000055, goodsConfig.getGoodsId());
        Assertions.assertEquals("玫瑰", goodsConfig.getGoodsName());
        Assertions.assertEquals(1, goodsConfig.getGoodsNum());
        log.info("goodsConfigList: {}", goodsConfigList);
    }

    @Test
    void parseJsonPathToTypeRef() {
        Map<String, ActivityConfig> activityConfigMap = JsonUtils.parseJsonPathToTypeRef(jsonObjectData, "$.activityConfig", new TypeRef<>() {});
        log.warn("activityConfigMap: {}", activityConfigMap);
        Assertions.assertEquals(3, activityConfigMap.size());
        List<BookInfo> bookInfoMap = JsonUtils.parseJsonPathToTypeRef(jsonObjectData, "$.store.book", new TypeRef<>() {});
        log.warn("bookInfoMap: {}", bookInfoMap);
        Assertions.assertEquals(4, bookInfoMap.size());
    }

    @Test
    void parseJsonPathObject() {
        BicycleConfig bicycleConfig = JsonUtils.parseJsonPathToObject(jsonObjectData, "$.store.bicycle", BicycleConfig.class);
        log.warn("bicycleConfig: {}", bicycleConfig);
        Assertions.assertEquals("19.95", bicycleConfig.getPrice().stripTrailingZeros().toPlainString());
        Assertions.assertEquals("red", bicycleConfig.getColor());
    }

    @Test
    void xml2json() {
        String json = JsonUtils.xml2json(xmlValidData);
        Assertions.assertEquals("{\"name\":\"Poppy\",\"color\":\"RED\",\"petals\":\"9\"}", json);
        Assertions.assertThrows(JsonParseException.class, () -> JsonUtils.xml2json(xmlInvalidData));
    }

    @Test
    void parseJsonPathGsonObject() {
        JsonObject jsonObject = JsonUtils.parseJsonPathGsonObject(jsonObjectData, "$.store.bicycle");
        log.warn("jsonObject: {}", jsonObject);
        Assertions.assertEquals("19.95", jsonObject.get("price").getAsBigDecimal().stripTrailingZeros().toPlainString());
        Assertions.assertEquals("red", jsonObject.get("color").getAsString());
    }

    @Test
    void parseJsonNode() {
        JsonNode jsonNode = JsonUtils.parseJsonNode(jsonObjectData);
        log.warn("jsonNode: {}", jsonNode);
        Assertions.assertEquals(10, jsonNode.get("expensive").asInt());
        Assertions.assertEquals("Nigel Rees", jsonNode.get("store").get("book").get(0).get("author").asText());
        Assertions.assertEquals("The Lord of the Rings", jsonNode.get("store").get("book").get(3).get("title").asText());
        Assertions.assertEquals("19.95", BigDecimal.valueOf(jsonNode.get("store").get("bicycle").get("price").asDouble()).stripTrailingZeros().toPlainString());
        Assertions.assertFalse(JsonUtils.parseJsonNode("").isTextual());
        Assertions.assertFalse(JsonUtils.parseJsonNode("").isNull());
        Assertions.assertTrue(JsonUtils.parseJsonNode("").isEmpty());
        Assertions.assertThrows(IllegalArgumentException.class, () -> JsonUtils.parseJsonNode(null));
        Assertions.assertThrows(JsonParseException.class, () -> JsonUtils.parseJsonNode("invalid json content"));
    }

    @Test
    void parseList() {
        List<GiftInfo> giftInfoList = JsonUtils.parseList(giftListJson, GiftInfo.class);
        Assertions.assertEquals(766, giftInfoList.size());
        Assertions.assertTrue(StringUtils.isNotEmpty(JsonUtils.toPrettyJSONString(giftInfoList)));
        Assertions.assertTrue(StringUtils.isNotEmpty(JsonUtils.toJSONString(giftInfoList)));
        Assertions.assertEquals("null", JsonUtils.toJSONString(null));
        Assertions.assertEquals("null", JsonUtils.toPrettyJSONString(null));
        Assertions.assertThrows(JsonParseException.class, () -> JsonUtils.parseList("invalid json content", GiftInfo.class));
        OrderDto orderDto1 = OrderDto.builder().orderNo("A1")
                .orderMills(System.currentTimeMillis())
                .status(0).createTime(new Date())
                .addressDto(AddressDto.builder().city("上海").build())
                .build();
        OrderDto orderDto2 = OrderDto.builder().orderNo("B2")
                .orderMills(System.currentTimeMillis())
                .status(1).createTime(new Date())
                .addressDto(AddressDto.builder().city("广州").build())
                .build();
        List<OrderDto> orderDtoList = Lists.newArrayList(orderDto1, orderDto2);
        String json = JsonUtils.toJSONString(orderDtoList);
        List<OrderDto> jacksonList = JsonUtils.parseList(json, OrderDto.class);
        Assertions.assertEquals(2, jacksonList.size());
    }

    @Test
    void parseMap() {
        String json = "{\"realName\": \"Jack\",\"realAge\": \"31\"}";
        Map<String, Object> dataMap = JsonUtils.parseMap(json, String.class, Object.class);
        Assertions.assertEquals("Jack", dataMap.get("realName"));
        Assertions.assertEquals(31, Integer.parseInt(dataMap.get("realAge").toString()));
    }

    @Test
    void toJSONString() {
        Map<String, Object> map = Map.of(
            "status", HttpStatus.SC_OK,
            "message", HttpStatus.SC_OK,
            "timestamp", System.currentTimeMillis()
        );
        String json = JsonUtils.toJSONString(map);
        Assertions.assertTrue(StringUtils.isNoneBlank(json));
        Assertions.assertEquals(JSON.toJSONString(map), json);
        Assertions.assertEquals(1, StringUtils.split(json, "\r\n").length);
    }

    /**
     * Fastjson在构建json数据时，如果发现添加的第二个对象跟添加的第一个对象一样，就会直接将第一个对象的引用放在第二个对象数据位置。
     */
    @Test
    void disableCircularReferenceDetect() {
        ArrayList<Object> list = new ArrayList<>();
        Object o = new Object();
        list.add(o);
        list.add(o);
        String enabledDetectJson = JSONObject.toJSONString(list);
        String disableDetectJson = JSONObject.toJSONString(list, SerializerFeature.DisableCircularReferenceDetect);
        String jacksonJson = JsonUtils.toJSONString(list);
        log.warn("enabledDetectJson: {}, disableDetectJson: {}, jacksonJson: {}", enabledDetectJson, disableDetectJson, jacksonJson);
        Assertions.assertNotEquals(jacksonJson, enabledDetectJson);
        Assertions.assertEquals(jacksonJson, disableDetectJson);
    }

    @Test
    void toPrettyJSONString() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", HttpStatus.SC_OK);
        map.put("message", HttpStatus.SC_OK);
        map.put("timestamp", System.currentTimeMillis());
        String json = JsonUtils.toPrettyJSONString(map);
        log.warn("json: \r\n{}", json);
        Assertions.assertTrue(StringUtils.isNoneBlank(json));
        Assertions.assertEquals(5, StringUtils.split(json, "\r\n").length);
    }

    @Test
    void parseObject() {
        OrderDto orderDto = OrderDto.builder().orderNo("123")
                .orderMills(System.currentTimeMillis())
                .status(0).createTime(new Date())
                .addressDto(AddressDto.builder().city("广州").build())
                .build();
        String json = JsonUtils.toJSONString(orderDto);
        OrderDto fastjson = JSON.parseObject(json, OrderDto.class);
        OrderDto jackson = JsonUtils.parseObject(json, OrderDto.class);
        Assertions.assertEquals(fastjson.getOrderNo(), jackson.getOrderNo());
        Assertions.assertEquals(fastjson.getOrderMills(), jackson.getOrderMills());
        Assertions.assertEquals(fastjson.getStatus(), jackson.getStatus());
        Assertions.assertEquals(fastjson.getCreateTime(), jackson.getCreateTime());
        Assertions.assertEquals(fastjson.getAddressDto().getCity(), jackson.getAddressDto().getCity());
    }

    @Test
    void parseObjectTypeReference() {
        OrderDto orderDto1 = OrderDto.builder().orderNo("A1")
                .orderMills(System.currentTimeMillis())
                .status(0).createTime(new Date())
                .addressDto(AddressDto.builder().city("上海").build())
                .build();
        OrderDto orderDto2 = OrderDto.builder().orderNo("B2")
                .orderMills(System.currentTimeMillis())
                .status(1).createTime(new Date())
                .addressDto(AddressDto.builder().city("广州").build())
                .build();
        List<OrderDto> orderDtoList = Lists.newArrayList(orderDto1, orderDto2);
        String json = JsonUtils.toJSONString(orderDtoList);
        List<OrderDto> fastjsonList = JSON.parseObject(json, new com.alibaba.fastjson.TypeReference<List<OrderDto>>() {
        });
        List<OrderDto> jacksonList = JsonUtils.parseObject(json, new com.fasterxml.jackson.core.type.TypeReference<>() {
        });
        Assertions.assertEquals(fastjsonList.size(), jacksonList.size());
        OrderDto fastjsonListFirst = fastjsonList.getFirst();
        OrderDto jacksonListFirst = jacksonList.getFirst();
        Assertions.assertEquals(fastjsonListFirst.getOrderMills(), jacksonListFirst.getOrderMills());
        Assertions.assertEquals(fastjsonListFirst.getStatus(), jacksonListFirst.getStatus());
        Assertions.assertEquals(fastjsonListFirst.getCreateTime(), jacksonListFirst.getCreateTime());
        Assertions.assertEquals(fastjsonListFirst.getAddressDto().getCity(), jacksonListFirst.getAddressDto().getCity());
    }

    @Test
    void parseArray() {
        OrderDto orderDto1 = OrderDto.builder().orderNo("A1")
                .orderMills(System.currentTimeMillis())
                .status(0).createTime(new Date())
                .addressDto(AddressDto.builder().city("上海").build())
                .build();
        OrderDto orderDto2 = OrderDto.builder().orderNo("B2")
                .orderMills(System.currentTimeMillis())
                .status(1).createTime(new Date())
                .addressDto(AddressDto.builder().city("广州").build())
                .build();
        List<OrderDto> orderDtoList = Lists.newArrayList(orderDto1, orderDto2);
        String json = JsonUtils.toJSONString(orderDtoList);
        List<OrderDto> fastjsonList = JSON.parseArray(json, OrderDto.class);
        List<OrderDto> jacksonList = JsonUtils.parseArray(json, OrderDto.class);
        Assertions.assertEquals(fastjsonList.size(), jacksonList.size());
        OrderDto fastjsonListFirst = fastjsonList.getFirst();
        OrderDto jacksonListFirst = jacksonList.getFirst();
        Assertions.assertEquals(fastjsonListFirst.getOrderMills(), jacksonListFirst.getOrderMills());
        Assertions.assertEquals(fastjsonListFirst.getStatus(), jacksonListFirst.getStatus());
        Assertions.assertEquals(fastjsonListFirst.getCreateTime(), jacksonListFirst.getCreateTime());
        Assertions.assertEquals(fastjsonListFirst.getAddressDto().getCity(), jacksonListFirst.getAddressDto().getCity());
    }

    @Test
    void annotationJSONField() {
        String json = "{\"realName\": \"Jack\",\"realAge\": \"31\"}";
        UserDto jackson = JsonUtils.parseObject(json, UserDto.class);
        UserDto fastjson = JSON.parseObject(json, UserDto.class);
        Assertions.assertEquals("Jack", jackson.getName());
        Assertions.assertEquals(31, jackson.getAge());
        Assertions.assertEquals(fastjson.getName(), jackson.getName());
        Assertions.assertEquals(fastjson.getAge(), jackson.getAge());
    }

    @Test
    void convertMapToBean() {
        String json = "{\"realName\": \"Jack\",\"realAge\": \"31\"}";
        Map<String, String> dataMap = JsonUtils.parseObject(json, new TypeReference<>() {});
        UserDto userDto = JsonUtils.convertValue(dataMap, UserDto.class);
        Assertions.assertNotNull(userDto);
    }

    @Test
    void newJSONObject() {
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> dataMap = Maps.newHashMap();
        log.warn("json: {}", jsonObject.toJSONString());
        Assertions.assertEquals(jsonObject.toJSONString(), JsonUtils.toJSONString(dataMap));
    }

    @Test
    void parseObjectKeepAsRawJson() {
        AirwallexConfirmNextAction nextAction = JsonUtils.parseObject(wechatPayJson, AirwallexConfirmNextAction.class);
        Assertions.assertNotNull(nextAction);
        Assertions.assertTrue(StringUtils.isNoneBlank(nextAction.getData()));
    }

    @Test
    void isValidJson() {
        Assertions.assertFalse(JsonUtils.isValidJson(null));
        Assertions.assertFalse(JsonUtils.isValidJson(jsonInvalidData));
        Assertions.assertTrue(JsonUtils.isValidJson(StringUtils.SPACE));
        Assertions.assertTrue(JsonUtils.isValidJson(StringUtils.EMPTY));
        Assertions.assertTrue(JsonUtils.isValidJson(this.jsonArrayData));
        Assertions.assertTrue(JsonUtils.isValidJson(this.jsonObjectData));
        Assertions.assertTrue(JsonUtils.isValidJson(this.giftListJson));
        Assertions.assertTrue(JsonUtils.isValidJson(this.wechatPayJson));
    }

    /**
     * <pre>
     * [database]
     * url = "********************************"
     * max_connections = 100
     * min_connections = 5
     *
     * [server]
     * http_port = 8080
     * grpc_port = 9090
     * </pre>
     */
    @Test
    @SneakyThrows
    void readToml() {
        TomlMapper mapper = new TomlMapper();
        URL resourceURL = ResourceUtils.resourceToURL("test.toml");
        AppConfig appConfig = mapper.readValue(resourceURL, AppConfig.class);
        log.warn("appConfig: \n{}", JsonUtils.toPrettyJSONString(appConfig));
        Assertions.assertEquals("********************************", appConfig.getDatabase().getUrl());
        Assertions.assertEquals(100, appConfig.getDatabase().getMaxConnections());
        Assertions.assertEquals(5, appConfig.getDatabase().getMinConnections());
        Assertions.assertEquals(8080, appConfig.getServer().getHttpPort());
        Assertions.assertEquals(9090, appConfig.getServer().getGrpcPort());
    }

    @Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class GoodsConfig {
        private Map<String, String> precondition = Maps.newHashMap();
        private int goodsType;
        private long goodsId;
        private String goodsName;
        private int goodsNum;
    }

    @Data
    public static class ActivityConfig {
        private String tips;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date endTime;
    }

    @Data
    private static class BicycleConfig {
        private String color;
        private BigDecimal price;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AirwallexConfirmNextAction {
        private String content_type;
        @JsonDeserialize(using = KeepAsJsonDeserializer.class)
        private String data;
        private Map<String, String> dcc_data;
        private String method;
        private String qrcode;
        private String qrcode_url;
        private String stage;
        private String type;
        private String url;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class OrderDto {
        private String orderNo;
        private long orderMills;
        private int status;
        private Date createTime;
        private AddressDto addressDto;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class AddressDto {
        private String city;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class UserDto {
        @JSONField(name = "realName")
        @JsonProperty("realName")
        private String name;
        @JSONField(name = "realAge")
        @JsonProperty("realAge")
        private int age;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BookInfo {
        private String author;
        private Object price;
        private String isbn;
        private String category;
        private String title;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppConfig {
        @JsonProperty("database")
        private DatabaseConfig database;
        @JsonProperty("server")
        private ServerConfig server;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatabaseConfig {
        @JsonProperty("url")
        private String url;
        @JsonProperty("max_connections")
        private int maxConnections;
        @JsonProperty("min_connections")
        private int minConnections;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServerConfig {
        @JsonProperty("http_port")
        private int httpPort;
        @JsonProperty("grpc_port")
        private int grpcPort;
    }
}