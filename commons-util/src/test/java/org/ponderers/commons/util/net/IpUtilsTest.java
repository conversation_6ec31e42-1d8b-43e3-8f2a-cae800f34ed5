package org.ponderers.commons.util.net;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class IpUtilsTest {

    private static final String IPV4_LOCALHOST = "127.0.0.1";
    private static final String IPV6_LOCALHOST = "::1";
    private static final String IPV4_INTRANET = "************";
    private static final String IPV6_INTRANET = "0:0:0:0:0:ffff:c0a8:3801";

    @Mock
    private HttpServletRequest request;

    @Test
    void getServerIpAddress() {
        String serverIpAddress = IpUtils.getServerIpAddress("192.", "172.", "10.");
        log.warn("serverIpAddress: {}", serverIpAddress);
        Assertions.assertTrue(StringUtils.isNotBlank(serverIpAddress));
        Assertions.assertTrue(IpUtils.isValidAddress(serverIpAddress));
    }

    @Test
    void isValidAddress() {
        Assertions.assertTrue(IpUtils.isValidAddress(IPV4_LOCALHOST));
        Assertions.assertTrue(IpUtils.isValidAddress(IPV6_LOCALHOST));
        Assertions.assertTrue(IpUtils.isValidAddress(IPV4_INTRANET));
        Assertions.assertTrue(IpUtils.isValidAddress(IPV6_INTRANET));
    }

    @Test
    void isValidInet4Address() {
        Assertions.assertTrue(IpUtils.isValidInet4Address(IPV4_LOCALHOST));
        Assertions.assertFalse(IpUtils.isValidInet4Address(IPV6_LOCALHOST));
        Assertions.assertTrue(IpUtils.isValidInet4Address(IPV4_INTRANET));
        Assertions.assertFalse(IpUtils.isValidInet4Address(IPV6_INTRANET));
    }

    @Test
    void isValidInet6Address() {
        Assertions.assertFalse(IpUtils.isValidInet6Address(IPV4_LOCALHOST));
        Assertions.assertTrue(IpUtils.isValidInet6Address(IPV6_LOCALHOST));
        Assertions.assertFalse(IpUtils.isValidInet6Address(IPV4_INTRANET));
        Assertions.assertTrue(IpUtils.isValidInet6Address(IPV6_INTRANET));
    }

    @Test
    void inet4AddressToLong() {
        Assertions.assertEquals(-1062717439L, IpUtils.inet4AddressToLong(IPV4_INTRANET));
        Assertions.assertEquals(2130706433L, IpUtils.inet4AddressToLong(IPV4_LOCALHOST));
    }

    @Test
    void longToInet4Address() {
        Assertions.assertEquals(IPV4_INTRANET, IpUtils.longToInet4Address(-1062717439L));
        Assertions.assertEquals(IPV4_LOCALHOST, IpUtils.longToInet4Address(2130706433L));
    }

    @Test
    void getClientIpAddress() {
        when(request.getHeader("x-forwarded-for")).thenReturn("************00").thenReturn("");
        when(request.getRemoteAddr()).thenReturn("************01");
        Assertions.assertEquals("************00", IpUtils.getClientIpAddress(request));
        Assertions.assertEquals("************01", IpUtils.getClientIpAddress(request));
    }

    @Test
    void checkIp() {
        Assertions.assertTrue(IpUtils.checkIp("127.0.0.1"));
    }

    @Test
    void checkIpPort() {
        Assertions.assertFalse(IpUtils.checkIpPort("127.0.0.1", 80));
    }

    @Test
    void getSubnetInfo() {
        SubnetUtils.SubnetInfo subnetInfo = IpUtils.getSubnetInfo("***********/19");
        SubnetUtils.SubnetInfo subnetInfoCopy = IpUtils.getSubnetInfo("***********", "*************");
        Assertions.assertEquals(subnetInfo.toString(), subnetInfoCopy.toString());
        log.warn("subnetInfo: {}", subnetInfo);
        Assertions.assertEquals(8190L, subnetInfo.getAddressCountLong());
        Assertions.assertEquals("*************", subnetInfo.getNetmask());
        Assertions.assertEquals("***********", subnetInfo.getAddress());
        Assertions.assertEquals("***********", subnetInfo.getLowAddress());
        Assertions.assertEquals("*************", subnetInfo.getHighAddress());
        Assertions.assertEquals("*************", subnetInfo.getBroadcastAddress());
        Assertions.assertEquals("***********/19", subnetInfo.getCidrSignature());
    }

}