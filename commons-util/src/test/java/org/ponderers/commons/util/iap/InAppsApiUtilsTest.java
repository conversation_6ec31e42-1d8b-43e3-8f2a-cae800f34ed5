package org.ponderers.commons.util.iap;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.*;
import org.ponderers.commons.util.experimental.ResourceUtils;
import org.ponderers.commons.util.json.JsonUtils;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Timeout(value = 3000, unit = TimeUnit.SECONDS)
@Disabled("需要提供证书相关信息本地运行")
class InAppsApiUtilsTest {
    private static String jwtToken;

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(1000, TimeUnit.MILLISECONDS)
            .writeTimeout(3000, TimeUnit.MILLISECONDS)
            .readTimeout(3000, TimeUnit.MILLISECONDS)
            .retryOnConnectionFailure(true)
            .build();

    @BeforeAll
    static void beforeAll() {
        String config = ResourceUtils.resourceToString("inAppsConfig.json");
        InAppsConfig inAppsConfig = JsonUtils.parseObject(config, InAppsConfig.class);
        jwtToken = InAppsApiUtils.createIapJwtTokenByFilepath(inAppsConfig.getBid(), inAppsConfig.getKid(),
                inAppsConfig.getIss(), inAppsConfig.getP8filepath());
    }

    @Test
    void inAppsV1Transactions() {
        String transactionId = "470001667245395";
        Optional<TransactionInfo> optionalTransaction = InAppsApiUtils.inAppsV1Transactions(
                client, jwtToken, transactionId, false);
        Assertions.assertTrue(optionalTransaction.isPresent());
    }

    @Test
    void inAppsV1Lookup() {
        String orderId = "MQKNFXHGHB";
        Optional<LookupResponse> optionalLookupResponse = InAppsApiUtils.inAppsV1Lookup(client, jwtToken, orderId, false);
        Assertions.assertTrue(optionalLookupResponse.isPresent());
        LookupResponse lookupResponse = optionalLookupResponse.get();
        List<String> signedTransactions = lookupResponse.getSignedTransactions();
        signedTransactions.forEach(signedTransaction -> {
            TransactionInfo transactionInfo = InAppsApiUtils.parseSignedTransactionInfo(signedTransaction);
            log.warn("transaction: {}", transactionInfo);
        });
    }

    @Test
    void inAppsV1HistoryProductType() {
        String transactionId = "320001603604421";
        HistoryOptions historyOptions = HistoryOptions.builder()
                .sort(HistoryOptions.Sort.ASCENDING)
                .revision(Optional.empty())
                .build();
        Optional<HistoryResponse> optionalHistoryResponse = InAppsApiUtils.inAppsV1History(
                client, jwtToken, transactionId, historyOptions, false);
        Assertions.assertTrue(optionalHistoryResponse.isPresent());
        HistoryResponse historyResponse = optionalHistoryResponse.get();
        List<String> signedTransactions = historyResponse.getSignedTransactions();
        List<TransactionInfo> transactionInfos = signedTransactions.stream()
                .map(InAppsApiUtils::parseSignedTransactionInfo)
                .collect(Collectors.toList());
        transactionInfos.forEach(transactionInfo -> log.warn("transaction: {}", transactionInfo));
    }

    @Test
    void inAppsV1HistoryRevoked() {
        String transactionId = "290001704919456";
        HistoryOptions historyOptions = HistoryOptions.builder()
                .sort(HistoryOptions.Sort.DESCENDING)
                .revoked(Optional.of(true))
                .build();
        Optional<HistoryResponse> optionalHistoryResponse = InAppsApiUtils.inAppsV1History(
                client, jwtToken, transactionId, historyOptions, false);
        Assertions.assertTrue(optionalHistoryResponse.isPresent());
        HistoryResponse historyResponse = optionalHistoryResponse.get();
        List<String> signedTransactions = historyResponse.getSignedTransactions();
        List<TransactionInfo> transactionInfos = signedTransactions.stream()
                .map(InAppsApiUtils::parseSignedTransactionInfo)
                .collect(Collectors.toList());
        transactionInfos.forEach(transactionInfo -> log.warn("transaction: {}", transactionInfo));
    }

    @Test
    void inAppsV2RefundLookup() {
        String transactionId = "290001704919456";
        Optional<RefundLookupResponse> optionalRefundLookupResponse = InAppsApiUtils.inAppsV2RefundLookup(
                client, jwtToken, transactionId, "", false);
        Assertions.assertTrue(optionalRefundLookupResponse.isPresent());
        RefundLookupResponse refundLookupResponse = optionalRefundLookupResponse.get();
        refundLookupResponse.getSignedTransactions().forEach(signedTransaction -> {
            TransactionInfo transactionInfo = InAppsApiUtils.parseSignedTransactionInfo(signedTransaction);
            log.warn("transactionInfo: {}", transactionInfo);
        });
    }

    @Test
    void inAppsV1Subscriptions() {
        String transactionId = "370000597853336";
        Optional<SubscriptionsResponse> optionalSubscriptionsResponse = InAppsApiUtils.inAppsV1Subscriptions(
                client, jwtToken, transactionId, false);
        log.warn("optionalSubscriptionsResponse: {}", optionalSubscriptionsResponse);
        Assertions.assertTrue(optionalSubscriptionsResponse.isPresent());
        SubscriptionsResponse subscriptionsResponse = optionalSubscriptionsResponse.get();
        List<SubscriptionsResponse.DataItem> dataItems = subscriptionsResponse.getData();
        dataItems.forEach(dataItem -> {
            List<SubscriptionsResponse.LastTransactionsItem> lastTransactionsItems = dataItem.getLastTransactions();
            lastTransactionsItems.forEach(lastTransactionsItem -> {
                String originalTransactionId = lastTransactionsItem.getOriginalTransactionId();
                String signedTransactionInfo = lastTransactionsItem.getSignedTransactionInfo();
                TransactionInfo transactionInfo = InAppsApiUtils.parseSignedTransactionInfo(signedTransactionInfo);
                String signedRenewalInfo = lastTransactionsItem.getSignedRenewalInfo();
                RenewalInfo renewalInfo = InAppsApiUtils.parseSignedRenewalInfo(signedRenewalInfo);
                log.warn("originalTransactionId: {}, transaction: {}, renewalInfo: {}",
                        originalTransactionId, transactionInfo, renewalInfo);
            });
        });
    }
}