package org.ponderers.commons.util.experimental;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Map;

class TemplateUtilsTest {

    private static final String expected = "Hello, Kay! Welcome to China.";

    @Test
    void formatWithPositions() {
        String template = "Hello, {0}! Welcome to {1}.";
        String rendered = TemplateUtils.format(template, "Kay", "China");
        Assertions.assertEquals(expected, rendered);
    }

    @Test
    void formatWithoutPositions() {
        String template = "Hello, {}! Welcome to {}.";
        String rendered = TemplateUtils.format(template, "Kay", "China");
        Assertions.assertEquals(expected, rendered);
    }

    @Test
    void render() {
        String template = "Hello, {{name}}! Welcome to {{place}}.";
        Map<String, Object> context = Map.of("name", "Kay", "place", "China");
        String rendered = TemplateUtils.render(template, context);
        Assertions.assertEquals(expected, rendered);
    }
}