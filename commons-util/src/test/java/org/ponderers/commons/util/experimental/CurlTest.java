package org.ponderers.commons.util.experimental;

import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static org.ponderers.commons.util.experimental.Curl.STR_RESOLVER;

@Slf4j
@Disabled
class CurlTest {
    @Test
    void exec() {
        Curl curl = new Curl("https://totoro.ponderers.cn/infrastructure/actuator/health")
                .header("token: 159f3d79d02e020a0182f1fa944afd1d8a2b6ce90f6153fbac74c14300600ef2")
                .header("appId", "100426")
                .header(ImmutableMap.of("imei", "353823082516079", "mid", "f6326ff76a240358bf449eec7b93f790"))
                .timeout(1, 4);
        Assertions.assertTrue(curl.toString().contains("--location"));
        String responseBody = curl.exec(STR_RESOLVER, null);
        log.warn("curl: {}, responseBody: {}", curl, responseBody);
        Assertions.assertEquals(200, curl.getHttpCode());
    }
}