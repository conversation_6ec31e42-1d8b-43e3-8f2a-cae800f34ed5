package org.ponderers.commons.util.codec;

import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.encoders.Base64;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.crypto.PemUtils;
import org.ponderers.commons.util.experimental.ResourceUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.X509Certificate;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

class PemUtilsTest {

    @Test
    void parsePEMFile() {
        String pemKeyData = ResourceUtils.resourceToString("server_key.pem");
        pemKeyData = pemKeyData.replaceAll("\\n", "")
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "");
        System.out.println(pemKeyData);
        byte[] decodedKeyBytes1 = Base64.decode(pemKeyData);
        // Convert the PEM encoded key to a PrivateKey object
        byte[] decodedKeyBytes2;
        try (InputStream is2 = Thread.currentThread().getContextClassLoader().getResourceAsStream("server_key.pem")) {
            decodedKeyBytes2 = PemUtils.parsePEMFile(is2);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        assertArrayEquals(decodedKeyBytes1, decodedKeyBytes2);
    }

    @Test
    @SneakyThrows
    void getPrivateKey() {
        String message = "1694092595";
        byte[] decodedKeyBytes;
        try (InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("server_key.pem")) {
            decodedKeyBytes = PemUtils.parsePEMFile(is);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        PrivateKey privateKey = PemUtils.getPrivateKey(decodedKeyBytes, "RSA");
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message.getBytes(StandardCharsets.UTF_8));
        byte[] digitalSignature = signature.sign();
        String actualSignature = Base64.toBase64String(digitalSignature);
        System.out.println(actualSignature);
        String expectSignature = "Qy0kE/2INzXl+lM1tuMkhSYsfBXi8s1jYCEqE6oAeW1SJdXfmpHTPiloLLbz1xw0MgFzH0+ANXMk+ibNpGIOtPMsTv3yWo01R/yylhWPvRRIrq76F4BOJB6a6x6SvAoVYMlmUYCir9kGVFbR/nsY3AUlyABg/bMtUfpZa16jUHc=";
        Assertions.assertEquals(expectSignature, actualSignature);
    }

    @Test
    void getSerialNumber() {
        String certificateString = ResourceUtils.resourceToString("server_cert.crt");
        X509Certificate x509Certificate = PemUtils.loadX509FromString(certificateString);
        String serialNumber = PemUtils.getSerialNumber(x509Certificate);
        System.out.println(serialNumber);
        Assertions.assertTrue(StringUtils.isNoneBlank(serialNumber));
    }


}