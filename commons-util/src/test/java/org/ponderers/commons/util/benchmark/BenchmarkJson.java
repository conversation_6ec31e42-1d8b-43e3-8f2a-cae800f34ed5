package org.ponderers.commons.util.benchmark;

import com.alibaba.fastjson.JSON;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.ponderers.commons.util.experimental.ResourceUtils;
import org.ponderers.commons.util.json.JsonUtils;
import org.ponderers.commons.util.model.GiftInfo;

import java.util.List;
import java.util.concurrent.TimeUnit;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
@Fork(value = 2, jvmArgs = {"-Xms2G", "-Xmx2G"})
@Warmup(iterations = 3, time = 1)
@Measurement(iterations = 5, time = 1)
public class BenchmarkJson {

    @Param({"100000"})
    private int N;

    private List<GiftInfo> giftInfoList;

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(BenchmarkJson.class.getSimpleName())
                .forks(1)
                .build();
        new Runner(opt).run();
    }

    @Setup
    public void setup() {
        String content = ResourceUtils.resourceToString("giftList.json");
        this.giftInfoList = JsonUtils.parseList(content, GiftInfo.class);
    }

    @Benchmark
    public void fastjson(Blackhole bh) {
        List<GiftInfo> giftInfos = JSON.parseArray(JSON.toJSONString(giftInfoList), GiftInfo.class);
        bh.consume(giftInfos);
    }

    @Benchmark
    public void jackson(Blackhole bh) {
        List<GiftInfo> giftInfos = JsonUtils.parseArray(JsonUtils.toJSONString(giftInfoList), GiftInfo.class);
        bh.consume(giftInfos);
    }
}
