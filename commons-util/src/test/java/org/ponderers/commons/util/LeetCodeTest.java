package org.ponderers.commons.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class LeetCodeTest {

    @Test
    void run() {
        Assertions.assertTrue(Objects.deepEquals(new int[] {0, 3}, twoSum(new int[] {0,4,3,0}, 0)), "1. Two Sum");
        Assertions.assertEquals(-321, reverse(-123), "7. Reverse Integer");
        Assertions.assertEquals(21, reverse(120), "7. Reverse Integer");
        Assertions.assertFalse(isPalindrome(-121), "9. Palindrome Number");
        log.warn("groupAnagrams: {}", groupAnagrams(new String[] {"eat", "tea", "tan", "ate", "nat", "bat"}));
    }

    /**
     * 7. Reverse Integer
     */
    public static int reverse(int x) {
        char[] yStr = String.valueOf(x).toCharArray();
        for (int i = 0, j = yStr.length - 1; i <= j; i++, j--) {
            if (yStr[j] == 0) {
                i--;
                continue;
            }
            if (yStr[i] == '-') {
                j++;
                continue;
            }
            char tmp = yStr[j];
            yStr[j] = yStr[i];
            yStr[i] = tmp;
        }
        long xx = Long.parseLong(String.valueOf(yStr));
        if (xx > Integer.MAX_VALUE || xx < Integer.MIN_VALUE) return 0;
        return Integer.parseInt(String.valueOf(yStr));
    }

    /**
     * 1. Two Sum
     */
    public static int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> dataMap = new HashMap<>();
        for(int i=0;i<nums.length;i++) {
            dataMap.put(nums[i], i);
        }
        for(int i=0;i<nums.length;i++) {
            int other = target - nums[i];
            if (dataMap.containsKey(other) && dataMap.get(other) != i) {
                return new int[]{i, dataMap.get(other)};
            }
        }
        return new int[]{};
    }

    /**
     * 9. Palindrome Number
     */
    public static boolean isPalindrome(int x) {
        if (x < 0) return false;
        char[] xStr = String.valueOf(x).toCharArray();
        for (int i=0,j=xStr.length-1;i<xStr.length;i++,j--) {
            if (xStr[i] != xStr[j]) return false;
            if (i==j) return true;
        }
        return true;
    }

    /**
     * 49. 字母异位词分组
     */
    public static List<List<String>> groupAnagrams(String[] strs) {

        return null;
    }


}
