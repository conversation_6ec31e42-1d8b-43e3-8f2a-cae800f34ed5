package org.ponderers.commons.util.experimental;

import lombok.Data;
import lombok.SneakyThrows;
import org.apache.commons.lang3.JavaVersion;
import org.apache.commons.lang3.SystemUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ReflectionUtilsTest {

    @SneakyThrows
    @Test
    void writeStaticFinalField() {
        if (SystemUtils.isJavaVersionAtMost(JavaVersion.JAVA_12)) {
            DataBean dataBean = new DataBean();
            ReflectionUtils.writeStaticFinalField(dataBean.getClass(), "staticFinalField", "world");
            Object value = ReflectionUtils.readStaticField(dataBean.getClass(), "staticFinalField");
            Assertions.assertEquals("world", value.toString());
        }
    }

    @Test
    void writeStaticField() {
        DataBean dataBean = new DataBean();
        ReflectionUtils.writeStaticField(dataBean.getClass(), "staticField", "world");
        Assertions.assertEquals("world", DataBean.staticField);
    }

    @Test
    void writeField() {
        DataBean dataBean = new DataBean();
        ReflectionUtils.writeField(dataBean.getClass(), "field", dataBean, "world");
        Assertions.assertEquals("world", dataBean.getField());
    }

    @Test
    void invokeMethod() {
        DataBean dataBean = new DataBean();
        String ret1 = ReflectionUtils.invokeMethod(dataBean, "calculate");
        Assertions.assertEquals("hello", ret1);
        String ret2 = ReflectionUtils.invokeMethod(dataBean, "calculate", " world");
        Assertions.assertEquals("hello world", ret2);
    }

    @Data
    private static class DataBean {

        private static final String staticFinalField = "hello";
        private static String staticField = "hello";
        private String field = "hello";

        public String calculate() {
            return this.field;
        }

        public String calculate(String subString) {
            return this.field + subString;
        }
    }
}