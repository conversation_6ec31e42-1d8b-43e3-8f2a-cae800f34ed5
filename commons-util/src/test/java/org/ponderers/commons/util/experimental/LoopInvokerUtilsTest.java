package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class LoopInvokerUtilsTest {

    @Test
    void invoke() {
        StopWatch stopWatch = StopWatch.createStarted();
        List<Integer> resultList = LoopInvokerUtils.invoke(5, 10000,
                LoopInvokerUtilsTest::doAction, LoopInvokerUtilsTest::hasMoreChecker);
        stopWatch.stop();
        long seconds = TimeUnit.MILLISECONDS.toSeconds(stopWatch.getTime());
        log.warn("seconds: {}, resultList: {}", seconds, resultList);
        assertTrue(resultList.contains(5));
    }

    private static boolean hasMoreChecker(Integer integer) {
        return integer != 5;
    }

    private static int doAction(int i) {
        int result = new Random().nextInt(1, 20);
        log.warn("invoke. times: {}, result: {}", i, result);
        return result;
    }

    @Test
    void invokeNoReturn() {
        double permitsPerSecond = 5;
        int maxLoop = 15;
        StopWatch stopWatch = StopWatch.createStarted();
        LoopInvokerUtils.invoke(permitsPerSecond, maxLoop, i -> {
            int result = new Random().nextInt(1, 20);
            log.warn("invokeWithoutReturns. times: {}, result: {}", i, result);
        });
        stopWatch.stop();
        long seconds = Math.ceilDiv(stopWatch.getTime(), 1000);
        assertEquals(maxLoop / permitsPerSecond, seconds);
    }


}