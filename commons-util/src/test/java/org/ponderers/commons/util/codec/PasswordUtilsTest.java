package org.ponderers.commons.util.codec;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.ponderers.commons.util.crypto.PasswordUtils;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * Test PasswordUtils
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PasswordUtilsTest {

    private static byte[] password;
    private static byte[] salt;
    private static byte[] hash;

    @BeforeEach
    public void beforeClass() {
        Charset charset = StandardCharsets.UTF_8;
        password = PasswordUtils.generateRandomPassword(8).getBytes(charset);
        salt = PasswordUtils.getNextSalt();
        hash = PasswordUtils.hash(password, salt);
    }

    @AfterEach
    public void afterClass() {

    }

    @Test
    void getNextSalt() {
        byte[] salt = PasswordUtils.getNextSalt();
        Assertions.assertEquals(20, salt.length);
        String saltHex = Hex.encodeHexString(salt);
        Assertions.assertTrue(StringUtils.isNotEmpty(saltHex));
    }

    @Test
    void isValidPassword() {
        Assertions.assertTrue(PasswordUtils.isValidPassword(password, salt, hash));
    }

}