package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.file.CsvUtils;

@Slf4j
class CsvUtilsTest {

    String CSV_LINE_COMMA = "2022-01-03 02:44:53,商户消费,iCloud 由云上贵州运营,\"查询购买记录：reportaproblem.apple.com, 如需帮助请访问：support.apple.com\",支出,¥6.00,招商银行(****),支付成功,4200001400202201032134075809\t,MSM2ZJ0S9Ga0\t,\"/\"\n";
    String CSV_LINE_TAB = "APPLE\tUS\tcom.test.music.vip_luxury.auto.1month.txsp\t \t测试月卡+腾讯视频月卡（连续包月大会员）\t11.3.0\tIAY\t7\t17.19\t04/01/2023\t04/01/2023\tCNY\tCN\tCNY\t1611999605\t25.00\t \ttest20111011\tRenewal\t1 Month\tMusic\t\tiPhone\tiOS and watchOS\t \t \t \t \n";

    @Test
    void parseColumnsByComma() {
        String[] columns = CsvUtils.parseColumns(CSV_LINE_COMMA, ",", true);
        Assertions.assertEquals(11, columns.length);
        printArray(columns);
        Assertions.assertEquals("查询购买记录：reportaproblem.apple.com, 如需帮助请访问：support.apple.com", columns[3]);
        Assertions.assertEquals("MSM2ZJ0S9Ga0", columns[9]);
    }

    @Test
    void parseColumnsByTab() {
        String[] columns = CsvUtils.parseColumns(CSV_LINE_TAB, "\t", true);
        Assertions.assertEquals(28, columns.length);
        printArray(columns);
        Assertions.assertEquals("Music", columns[20]);
        Assertions.assertEquals("", columns[21]);
        Assertions.assertEquals("iPhone", columns[22]);
    }

    @Test
    void parseColumns() {
        String[] columns1 = CsvUtils.parseColumns(CSV_LINE_COMMA, "\t", true);
        String[] columns2 = CsvUtils.parseColumns(CSV_LINE_COMMA, "\t");
        Assertions.assertEquals(columns1.length, columns2.length);
        String[] columns3 = CsvUtils.parseColumns(CSV_LINE_TAB, "\t", true);
        String[] columns4 = CsvUtils.parseColumns(CSV_LINE_TAB, "\t");
        Assertions.assertEquals(columns3.length, columns4.length);
    }

    private void printArray(String[] columns) {
        for (int i = 0; i < columns.length; i++) {
            String column = columns[i];
            log.warn("column[{}]: {}", i, column);
        }
    }
}