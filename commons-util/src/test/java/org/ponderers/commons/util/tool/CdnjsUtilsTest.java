package org.ponderers.commons.util.tool;

import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.ponderers.commons.util.BaseTest;

import java.io.File;

/**
 * Test CdnjsUtils
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CdnjsUtilsTest extends BaseTest {

    @SneakyThrows
    @BeforeEach
    public void before() {
        File file = new File(CdnjsUtils.DEFAULT_STORAGE_BASE_PATH);
        if (file.exists() && file.isDirectory()) {
            FileUtils.deleteDirectory(file);
        }
    }

    @Disabled
    @Test
    void fetchCdnJSResource() {
        Assertions.assertTrue(CdnjsUtils.fetchCdnJSResource("jquery", "3.5.1"));
    }

    @SneakyThrows
    @AfterEach
    public void after() {
        File file = new File(CdnjsUtils.DEFAULT_STORAGE_BASE_PATH);
        if (file.exists() && file.isDirectory()) {
            FileUtils.deleteDirectory(file);
        }
    }
}