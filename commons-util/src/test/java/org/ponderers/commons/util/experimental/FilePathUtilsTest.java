package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@Slf4j
class FilePathUtilsTest {

    @Test
    void file() {
        File file = FilePathUtils.file("~/../.././README.md");
        assertFalse(file.exists());
    }

    @Test
    void resolveRelativePath() {
        Path path = FilePathUtils.resolveRelativePath("~/../.././README.md");
        log.warn("path: {}", path);
        assertEquals("/README.md", path.toString());
    }
}