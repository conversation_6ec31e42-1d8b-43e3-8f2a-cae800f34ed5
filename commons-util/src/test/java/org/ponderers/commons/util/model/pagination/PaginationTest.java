package org.ponderers.commons.util.model.pagination;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PaginationTest {
    @Test
    void testNormalPage() {
        Pagination pagination = Pagination.builder(5, 10).build();
        pagination.calculatePagination(100);
        Assertions.assertEquals(1, pagination.getFirstPage(), "getFirstPage");
        Assertions.assertEquals(4, pagination.getPrevPage(), "getPrevPage");
        Assertions.assertEquals(5, pagination.getCurrPage(), "getCurrPage");
        Assertions.assertEquals(6, pagination.getNextPage(), "getNextPage");
        Assertions.assertEquals(10, pagination.getLastPage(), "getLastPage");
        Assertions.assertEquals(10, pagination.getTotalPage(), "getTotalPage");
        Assertions.assertEquals(40, pagination.getOffset(), "getOffset");
        Assertions.assertEquals(10, pagination.getLimit(), "getLimit");
        Assertions.assertEquals(pagination.getLastPage(), pagination.getTotalPage(), "getTotalPage");
    }

    @Test
    void testCurrPage() {
        Pagination pagination = Pagination.builder(-1, 10).build();
        Assertions.assertEquals(1, pagination.getCurrPage(), "getCurrPage");
    }

    @Test
    void testPageSize() {
        Pagination pagination = Pagination.builder(1, -1).build();
        Assertions.assertEquals(1, pagination.getPageSize(), "getPageSize");
    }

    @Test
    void testToString() {
        Pagination pagination = Pagination.builder(5, 10).build();
        pagination.calculatePagination(100);
        String toString = "Pagination(currPage=5, pageSize=10, totalCount=100, totalPage=10, firstPage=1, prevPage=4, nextPage=6, lastPage=10, offset=40, limit=10)";
        Assertions.assertEquals(toString, pagination.toString(), "toString");
    }
}