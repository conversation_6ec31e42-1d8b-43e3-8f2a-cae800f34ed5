package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;


@Slf4j
class SystemUtilsTest {
    @Test
    void getProcessId() {
        Optional<Long> optionalProcessId = SystemUtils.getProcessId();
        log.warn("optionalProcessId: {}", optionalProcessId);
        Assertions.assertTrue(optionalProcessId.isPresent());
        Assertions.assertTrue(optionalProcessId.get() > 0);
    }
}