package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
class BigDecimalUtilsTest {

    @Test
    void isNegative() {
        Assertions.assertFalse(BigDecimalUtils.isNegative(BigDecimal.TEN));
        Assertions.assertFalse(BigDecimalUtils.isNegative(BigDecimal.ZERO));
        Assertions.assertTrue(BigDecimalUtils.isNegative(BigDecimal.valueOf(-1)));
    }

    @Test
    void isPositive() {
        Assertions.assertTrue(BigDecimalUtils.isPositive(BigDecimal.TEN));
        Assertions.assertFalse(BigDecimalUtils.isPositive(BigDecimal.ZERO));
        Assertions.assertFalse(BigDecimalUtils.isPositive(BigDecimal.valueOf(-1)));
    }

    @Test
    void isZero() {
        Assertions.assertFalse(BigDecimalUtils.isZero(BigDecimal.TEN));
        Assertions.assertTrue(BigDecimalUtils.isZero(BigDecimal.ZERO));
        Assertions.assertFalse(BigDecimalUtils.isZero(BigDecimal.valueOf(-1)));
    }

    @Test
    void divide() {
        BigDecimal result = BigDecimalUtils.divide(BigDecimal.valueOf(1), BigDecimal.valueOf(3));
        Assertions.assertEquals(0, BigDecimal.valueOf(0.33).compareTo(result));
    }

    @Test
    void multiply() {
        BigDecimal result = BigDecimalUtils.multiply(BigDecimal.valueOf(0.12), BigDecimal.valueOf(500));
        Assertions.assertEquals(0, BigDecimal.valueOf(60).compareTo(result));
        result = BigDecimalUtils.multiply(BigDecimal.valueOf(54.254), BigDecimal.valueOf(14.20));
        Assertions.assertEquals(0, BigDecimal.valueOf(770.41).compareTo(result.setScale(2, RoundingMode.HALF_UP)));
    }
}
