package org.ponderers.commons.util.codec;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.crypto.SignUtils;

@Slf4j
class SignUtilsTest {

    @Test
    void makeSign() {
        SignUtils.SignTypeEnum signTypeEnum = SignUtils.SignTypeEnum.SHA_256;
        log.warn("signTypeEnum: {}", signTypeEnum);
        Assertions.assertTrue(signTypeEnum.toString().contains("SHA-256"));
    }
}