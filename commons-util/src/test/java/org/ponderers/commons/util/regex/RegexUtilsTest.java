package org.ponderers.commons.util.regex;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RegexUtilsTest {

    @Test
    public void isEmail() {
        Assertions.assertTrue(RegexUtils.isEmail("<EMAIL>"));
    }

    @Test
    public void isPhone() {
        Assertions.assertTrue(RegexUtils.isPhone("15011931945"));
    }

    @Test
    public void isDigit() {
        Assertions.assertTrue(RegexUtils.isDigit("2367645"));
    }

    @Test
    public void isLetterDigitChinese() {
        Assertions.assertTrue(RegexUtils.isLetterDigitChinese("邮箱Email43"));
    }

    @Test
    public void isSplitByComma() {
        Assertions.assertTrue(RegexUtils.isSplitByComma("1,2,3"));
    }
}