package org.ponderers.commons.util.http.response;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.ponderers.commons.util.experimental.Payload;
import org.ponderers.commons.util.json.JsonUtils;
import org.ponderers.commons.util.model.JsonResult;
import org.ponderers.commons.util.model.status.SystemResultCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * JsonResultTest
 *
 * <AUTHOR>
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class JsonResultTest {

    private static final String SUCCESS_RESULT_MSG = "获取数据成功！";
    private static final String FAILURE_RESULT_MSG = "获取数据失败！";

    @Test
    void testJsonSuccessResult() {
        List<String> dataList = Lists.newArrayList("data1", "data2", "data3");
        JsonResult<List<String>> jsonResult1 = JsonResult.result(SystemResultCode.SUCCESS, SUCCESS_RESULT_MSG, dataList);
        JsonResult<List<String>> jsonResult2 = JsonResult.result(SystemResultCode.SUCCESS, dataList);
        String prettyJson1 = JsonUtils.toJSONString(jsonResult1);
        String prettyJson2 = JsonUtils.toJSONString(jsonResult2);
        Assertions.assertTrue(prettyJson1.contains("code") && prettyJson1.contains("msg") && prettyJson1.contains("data"));
        Assertions.assertTrue(prettyJson2.contains("code") && prettyJson2.contains("msg") && prettyJson2.contains("data"));
        Assertions.assertEquals(SUCCESS_RESULT_MSG, jsonResult1.getMsg());
        Assertions.assertEquals(SystemResultCode.SUCCESS.getDesc(), jsonResult2.getMsg());
    }

    @Test
    void testJsonFailureResult() {
        List<String> dataList = Lists.newArrayList("data1", "data2", "data3");
        JsonResult<List<String>> jsonResult1 = JsonResult.result(SystemResultCode.FAILURE, dataList);
        JsonResult<List<String>> jsonResult2 = JsonResult.result(SystemResultCode.FAILURE, dataList);
        JsonResult<List<String>> jsonResult3 = JsonResult.result(SystemResultCode.FAILURE, FAILURE_RESULT_MSG, dataList);
        String prettyJson1 = JsonUtils.toJSONString(jsonResult1);
        String prettyJson2 = JsonUtils.toJSONString(jsonResult2);
        String prettyJson3 = JsonUtils.toJSONString(jsonResult3);
        Assertions.assertTrue(prettyJson1.contains("code") && prettyJson3.contains("msg") && prettyJson3.contains("data"));
        Assertions.assertTrue(prettyJson2.contains("code") && prettyJson3.contains("msg") && prettyJson3.contains("data"));
        Assertions.assertTrue(prettyJson3.contains("code") && prettyJson3.contains("msg") && prettyJson3.contains("data"));
    }

    @Test
    void testDifferentDataTypeResult() {
        JsonResult<List<String>> listJsonResult = JsonResult.result(SystemResultCode.SUCCESS, Lists.newArrayList());
        Assertions.assertTrue(JsonUtils.toJSONString(listJsonResult).contains("\"data\":[]"));
        JsonResult<Map<String, Object>> mapJsonResult = JsonResult.result(SystemResultCode.SUCCESS, Maps.newHashMap());
        Assertions.assertTrue(JsonUtils.toJSONString(mapJsonResult).contains("\"data\":{}"));
        JsonResult<Set<String>> setJsonResult = JsonResult.result(SystemResultCode.SUCCESS, Sets.newHashSet());
        Assertions.assertTrue(JsonUtils.toJSONString(setJsonResult).contains("\"data\":[]"));
        JsonResult<String> objJsonResult = JsonResult.result(SystemResultCode.SUCCESS, StringUtils.EMPTY);
        Assertions.assertTrue(JsonUtils.toJSONString(objJsonResult).contains("\"data\":\"\""));
    }

    @Test
    void result() {
        JsonResult<Payload> jsonResult = JsonResult.result(SystemResultCode.SUCCESS);
        Assertions.assertNull(jsonResult.getData());
        jsonResult = JsonResult.result(1000, "third part error msg", Payload.createDefault());
        Assertions.assertEquals(1000, jsonResult.getCode());
        Assertions.assertEquals("third part error msg", jsonResult.getMsg());
        jsonResult = JsonResult.result(SystemResultCode.SUCCESS, resultCode -> resultCode.getDesc() + "suffix",  Payload.createDefault());
        Assertions.assertEquals(SystemResultCode.SUCCESS.getDesc() + "suffix", jsonResult.getMsg());
    }
}