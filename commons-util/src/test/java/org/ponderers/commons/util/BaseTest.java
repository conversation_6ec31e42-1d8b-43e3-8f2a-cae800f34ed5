package org.ponderers.commons.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.ponderers.commons.util.time.DateHelper;

/**
 * @see <a href="https://blog.mimacom.com/migrating-rules-to-junit5/#:~:text=One%20of%20the%20motivations%20for%20JUnit5%20was%20to,void%20name%28%29%20%7B%20new%20Item%28%29.update%28%22newValue%22%29%3B%20eventRule.verifyEventType%28DomainEvent.class%29%3B%20%7D%20%7D">https://blog.mimacom.com/migrating-rules-to-junit5/#:~:text=One%20of%20the%20motivations%20for%20JUnit5%20was%20to,void%20name%28%29%20%7B%20new%20Item%28%29.update%28%22newValue%22%29%3B%20eventRule.verifyEventType%28DomainEvent.class%29%3B%20%7D%20%7D</a>
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public abstract class BaseTest {

    protected final StopWatch stopWatch = new StopWatch(getClass().getSimpleName());

    @BeforeEach
    public void initClient(TestInfo testInfo) {
        log.info("------------------- Test Method <{}> ---------------------------", getDisplayName(testInfo));
        stopWatch.start();
    }

    @AfterEach
    public void after(TestInfo testInfo) {
        stopWatch.stop();
        if (stopWatch.getNanoTime() >= 0) {
            log.info("StopWatch '{}': running time = {}", stopWatch.getMessage(),
                    DateHelper.humanReadableFormat(stopWatch.getTime()));
        }
        log.info("------------------- Test Method <{}> ---------------------------", getDisplayName(testInfo));
    }

    private static String getDisplayName(TestInfo testInfo) {
        String testClassName = testInfo.getTestClass().map(Class::getSimpleName).orElse("");
        return StringUtils.join(testClassName, ".", testInfo.getDisplayName());
    }

}
