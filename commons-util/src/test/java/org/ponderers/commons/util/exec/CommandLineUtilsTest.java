package org.ponderers.commons.util.exec;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.CommandLine;
import org.apache.commons.exec.ExecuteException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@Slf4j
class CommandLineUtilsTest {

    private static final CommandLine commandLine1 = CommandLine.parse("nc -zvw2 127.0.0.1 80");
    private static final CommandLine commandLine2 = CommandLine.parse("java -version");
    private static final CommandLine commandLine3 = CommandLine.parse("who");

    @Test
    void syncExecute() {
        ExecuteException thrown = Assertions.assertThrows(ExecuteException.class, () -> {
            CommandLineUtils.syncExecute(commandLine1, TimeUnit.MILLISECONDS.toMillis(500));
        }, "ExecuteException was expected");
        Assertions.assertEquals("Process exited with an error: 1 (Exit value: 1)", thrown.getMessage());
        Assertions.assertEquals(0, CommandLineUtils.syncExecute(commandLine2, TimeUnit.MILLISECONDS.toMillis(500)));
        Assertions.assertEquals(0, CommandLineUtils.syncExecute(commandLine3, TimeUnit.MILLISECONDS.toMillis(500)));
    }

    @Test
    @Timeout(value = 2000, unit = TimeUnit.MILLISECONDS)
    void asyncExecute() {
        Consumer<Integer> onSuccess = exitValue -> Assertions.assertEquals(0, exitValue);
        Consumer<ExecuteException> onFailure = e -> log.warn("异步执行异常", e);
        CommandLineUtils.asyncExecute(commandLine1, TimeUnit.MILLISECONDS.toMillis(500), onSuccess, onFailure);
        CommandLineUtils.asyncExecute(commandLine2, TimeUnit.MILLISECONDS.toMillis(500), onSuccess, onFailure);
        CommandLineUtils.asyncExecute(commandLine3, TimeUnit.MILLISECONDS.toMillis(500), onSuccess, onFailure);
    }

}
