package org.ponderers.commons.util.thread;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.rng.UniformRandomProvider;
import org.apache.commons.rng.simple.RandomSource;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.ponderers.commons.util.time.DateHelper;

import java.time.Duration;
import java.util.Date;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Slf4j
class ExecutorUtilsTest {

    private static final UniformRandomProvider rng = RandomSource.TWO_CMRES_SELECT.create((Object) null, 6, 9);

    @Test
    void createPoolMeterRegistry() {
        MeterRegistry meterRegistry = new SimpleMeterRegistry();
        ExecutorService executorService = ExecutorUtils.createPool("worker", meterRegistry);
        CompletableFuture<Void> future = CompletableFuture.allOf(IntStream.rangeClosed(1, 10)
                .mapToObj(value -> CompletableFuture.runAsync(ExecutorUtilsTest::sleepAndLog, executorService))
                .toArray(CompletableFuture[]::new));
        future.join();
        log.warn("main: {}, future.isDone: {}", Thread.currentThread().getName(), future.isDone());
    }

    @SneakyThrows
    private static void sleepAndLog() {
        TimeUnit.MILLISECONDS.sleep(Duration.ofMillis(rng.nextInt(100, 200)).toMillis());
        log.warn("thread: {}", Thread.currentThread().getName());
    }

    @Test
    void createPool() {
        ExecutorService executorService = ExecutorUtils.createPool("worker");
        CompletableFuture<Void> future = CompletableFuture.allOf(IntStream.rangeClosed(1, 10)
                .mapToObj(value -> CompletableFuture.runAsync(ExecutorUtilsTest::sleepAndLog, executorService))
                .toArray(CompletableFuture[]::new));
        future.join();
        log.warn("main: {}, future.isDone: {}", Thread.currentThread().getName(), future.isDone());
    }

    @Test
    void createPoolRejected() {
        ExecutorConfig executorConfig = ExecutorConfig.custom("worker");
        executorConfig.setCorePoolSize(1);
        executorConfig.setMaximumPoolSize(1);
        executorConfig.setQueueCapacity(1);
        executorConfig.setHandler(ExecutorConfig.ABORT_POLICY);
        try (ExecutorService executorService = ExecutorUtils.createPool(executorConfig)) {
            Assertions.assertThrows(RejectedExecutionException.class, () -> {
                CompletableFuture<Void> future = CompletableFuture.allOf(IntStream.rangeClosed(1, 10)
                        .mapToObj(value -> CompletableFuture.runAsync(ExecutorUtilsTest::sleepAndLog, executorService))
                        .toArray(CompletableFuture[]::new));
                future.join();
                log.warn("main: {}, future.isDone: {}", Thread.currentThread().getName(), future.isDone());
            });
        }
    }

    @Test
    @SneakyThrows
    void createScheduledPool() {
        int count = 2;
        CountDownLatch latch = new CountDownLatch(count);
        final AtomicInteger times = new AtomicInteger();
        try (ExecutorService executorService = ExecutorUtils.createScheduledPool("schedule", () -> {
            int value = times.incrementAndGet();
            latch.countDown();
            log.warn("thread: {}, time: {}, value: {}",
                    Thread.currentThread().getName(), DateHelper.format(new Date()), value);
        }, 0, 1, TimeUnit.SECONDS)) {
            latch.await();
            Assertions.assertEquals(count, times.get());
        }
    }

    @SneakyThrows
    @Test
    void newVirtualThreadPerTaskExecutor() {
        int count = 1_000;
        CountDownLatch latch = new CountDownLatch(count);
        ExecutorService es = Executors.newVirtualThreadPerTaskExecutor();
        for (int i = 0; i < count; i++) {
            es.submit(() -> waitAndReturn(10, "aa", latch));
        }
        latch.await();
        es.close();
    }

    @SneakyThrows
    @Test
    void createPool2() {
        int count = 1_000;
        CountDownLatch latch = new CountDownLatch(count);
        ExecutorService es = ExecutorUtils.createPool("xxx");
        for (int i = 0; i < count; i++) {
            es.submit(() -> waitAndReturn(10, "bb", latch));
        }
        latch.await();
        es.close();
    }

    private CompletableFuture<String> waitAndReturn(long waitMills, String returnValue) {
        return waitAndReturn(waitMills, returnValue, null);
    }

    private CompletableFuture<String> waitAndReturn(long waitMills, String returnValue, CountDownLatch latch) {
        return CompletableFuture.supplyAsync(() -> {
            String ret = slowOperation(waitMills, returnValue);
            if (latch != null) latch.countDown();
            return ret;
        });
    }

    @SneakyThrows
    private static String slowOperation(long waitMills, String returnValue) {
        Thread.sleep(waitMills);
        log.warn("returnValue: {}", returnValue);
        return returnValue;
    }

    @Test
    void join() {
        CompletableFuture<String> future = waitAndReturn(100, "Harry");
        log.warn("join: {}", future.join());
    }

    @Test
    @SneakyThrows
    void allOf() {
        CompletableFuture<String> fn1 = waitAndReturn(10, "Lucy");
        CompletableFuture<String> fn2 = waitAndReturn(20, "Lily");
        CompletableFuture<Void> combinedFutures = CompletableFuture.allOf(fn1, fn2);
        combinedFutures.get(1000, TimeUnit.MILLISECONDS);
    }
}