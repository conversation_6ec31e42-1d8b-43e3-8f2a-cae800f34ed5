package org.ponderers.commons.util.experimental;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

@Slf4j
class DateRangeTest {

    @Test
    void getDatesBetween() {
        DateRange dateRange = new DateRange(1, (int) TimeUnit.DAYS.toMinutes(2));
        log.warn("dateRange: {}", dateRange);
        log.warn("betweenDates: {}", dateRange.getDatesBetween());
        Assertions.assertEquals(1, dateRange.getDatesBetween().size());
    }
}