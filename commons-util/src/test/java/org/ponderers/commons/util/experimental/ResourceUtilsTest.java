package org.ponderers.commons.util.experimental;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Properties;

class ResourceUtilsTest {

    private static final String RESOURCE_FILE_NAME = "json_array_data.json";

    @Test
    void resourceToString() {
        try (InputStream inputStream = Thread.currentThread().getContextClassLoader()
                .getResourceAsStream(RESOURCE_FILE_NAME)) {
            Objects.requireNonNull(inputStream);
            String expected = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            String actual = ResourceUtils.resourceToString(RESOURCE_FILE_NAME);
            Assertions.assertEquals(expected, actual);
        } catch (Exception e) {
            throw new ContextedRuntimeException(e);
        }
    }

    @Test
    void resourceToURL() {
        try (InputStream is1 = Thread.currentThread().getContextClassLoader()
                .getResourceAsStream(RESOURCE_FILE_NAME)) {
            Objects.requireNonNull(is1);
            String expect = IOUtils.toString(is1, StandardCharsets.UTF_8);
            URL url = ResourceUtils.resourceToURL(RESOURCE_FILE_NAME);
            try (InputStream is2 = url.openStream()) {
                String actual = IOUtils.toString(is2, StandardCharsets.UTF_8);
                Assertions.assertEquals(expect, actual);
            }
        } catch (Exception e) {
            throw new ContextedRuntimeException(e);
        }
    }

    @Test
    void resourceToProperties() {
        Properties properties = ResourceUtils.resourceToProperties("properties.conf");
        Assertions.assertEquals("1.0", properties.getProperty("version"));
        Assertions.assertEquals("TestApp", properties.getProperty("name"));
        Assertions.assertEquals("2016-11-12", properties.getProperty("date"));
    }

    @Test
    void resourceToPropertiesEmpty() {
        Properties properties = ResourceUtils.resourceToProperties("notExists.conf");
        Assertions.assertTrue(properties.isEmpty());
    }
}