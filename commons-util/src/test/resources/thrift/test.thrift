namespace java org.ponderers.commons.util.rpc.tool

# thrift --gen java --out ./gen resources/thrift/test.thrift

struct QueryOrderReq {
    1:required i64 userId,
    2:required i32 appId,
    3:required i32 timestamp,
    4:required string ext,
    5:required string sign
}

struct OrderVO {
    1:required i64 orderId,
    2:required OrderStatus orderStatus
}

enum OrderStatus {
    UNPAYED = 1,
    PAYED = 2,
    CANCELED = 3
}

struct OrderDataRsp {
    1:required i32 code,
    2:required string msg,
    5:required list<OrderVO> data
}

service OrderService {
    OrderDataRsp queryOrderByUserId(1:required QueryOrderReq request);
}
