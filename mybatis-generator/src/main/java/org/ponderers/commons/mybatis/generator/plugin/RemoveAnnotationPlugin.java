package org.ponderers.commons.mybatis.generator.plugin;

import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.api.dom.java.*;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * A MyBatis Generator plugin to remove annotations.
 * For example, remove @Results and @Result annotations.
 *
 * <AUTHOR>
 */
public class RemoveAnnotationPlugin extends PluginAdapter {

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public boolean clientGenerated(Interface interfaze, IntrospectedTable introspectedTable) {
        FullyQualifiedJavaType paramAnnotation = new FullyQualifiedJavaType("org.apache.ibatis.annotations.Param");
        FullyQualifiedJavaType myBatisRepositoryAnnotation = new FullyQualifiedJavaType("org.ponderers.boot.mybatis.annotation.MyBatisRepository");

        // 打印注解信息用于调试
        System.out.println("Initial Annotations: " + interfaze.getAnnotations());

        // 移除 @Mapper 注解
        interfaze.getAnnotations().removeIf(annotation -> annotation.equals("@Mapper"));

        // 添加必要的导入语句
        interfaze.addImportedType(myBatisRepositoryAnnotation);
        // 添加自定义注解
        interfaze.addAnnotation("@MyBatisRepository");
        for (Method method : interfaze.getMethods()) {
            // 增加@Param参数注解
//            addParamAnnotation(method);
            // 删除jdbcType注解内容
            removeJdbcTypeAnnotation(method);
            // 删除@Results注解内容
            removeResultsAnnotation(method);
        }
        return true;
    }

    private static void addParamAnnotation(Method method) {
        for (Parameter parameter : method.getParameters()) {
            String paramName = parameter.getName();
            parameter.addAnnotation("@Param(\"" + paramName + "\")");
        }
    }

    private static void removeJdbcTypeAnnotation(Method method) {
        List<String> annotations = method.getAnnotations();
        for (int i = 0; i < annotations.size(); i++) {
            String annotation = annotations.get(i);
            if (annotation.contains(",jdbcType=")) {
                annotations.set(i, annotation.replaceAll(",jdbcType=\\w+", ""));
            }
        }
    }

    private static void removeResultsAnnotation(Method method) {
        boolean enterResultAnnotation = false;
        Iterator<String> iterator = method.getAnnotations().iterator();
        while (iterator.hasNext()) {
            String annotation = iterator.next();
            if (annotation.startsWith("@Results({")) {
                iterator.remove();
                enterResultAnnotation = true;
                continue;
            }
            if (annotation.startsWith("})") && enterResultAnnotation) {
                iterator.remove();
                enterResultAnnotation = false;
                continue;
            }
            if (annotation.contains("@Result")) {
                iterator.remove();
            }
        }
    }


}