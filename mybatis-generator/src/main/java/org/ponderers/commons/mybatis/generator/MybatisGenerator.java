package org.ponderers.commons.mybatis.generator;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.generator.api.MyBatisGenerator;
import org.mybatis.generator.config.Configuration;
import org.mybatis.generator.config.xml.ConfigurationParser;
import org.mybatis.generator.internal.DefaultShellCallback;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MybatisGenerator {
    @SneakyThrows
    public static void main(String[] args) {
        List<String> warnings = new ArrayList<>();
        ConfigurationParser parser = new ConfigurationParser(warnings);
        try (InputStream inputStream = MybatisGenerator.class.getClassLoader().getResourceAsStream("generatorConfig.xml")) {
            Configuration config = parser.parseConfiguration(inputStream);
            DefaultShellCallback callback = new DefaultShellCallback(false);
            MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config, callback, warnings);
            myBatisGenerator.generate(null);
            warnings.forEach(warning -> log.warn("warning: {}", warning));
        }
    }
}
