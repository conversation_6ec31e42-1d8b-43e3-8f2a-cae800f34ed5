<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <!--引入数据库配置文件以解耦-->
    <properties resource="config.properties"/>

    <context id="dsql" targetRuntime="MyBatis3Simple" defaultModelType="flat">

        <!-- 注意PluginAdapter顺序 -->
        <plugin type="org.ponderers.commons.mybatis.generator.plugin.RemoveMethodPlugin"/>
        <plugin type="org.ponderers.commons.mybatis.generator.plugin.LombokPlugin">
            <property name="data" value="true"/>
            <property name="builder" value="true"/>
            <property name="noArgsConstructor" value="true"/>
            <property name="allArgsConstructor" value="true"/>
        </plugin>
        <plugin type="org.ponderers.commons.mybatis.generator.plugin.RemoveAnnotationPlugin"/>

        <commentGenerator>
            <!-- 在生成的注解中去掉日期，防止生成后，因为日志变更导致需要 git 提交 -->
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.driverClassName}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.username}"
                        password="${jdbc.password}">
        </jdbcConnection>

        <javaModelGenerator targetPackage="${targetPackage}" targetProject="${targetProject}">
            <property name="enableSubPackages" value="true"/>
        </javaModelGenerator>

        <javaClientGenerator targetPackage="${targetPackage}" targetProject="${targetProject}" type="ANNOTATEDMAPPER"/>

        <table schema="schema" tableName="${tableName}"
               enableInsert="true"
               enableSelectByPrimaryKey="true"
               enableUpdateByPrimaryKey="true"
               enableDeleteByPrimaryKey="false"
               enableCountByExample="false"
               enableDeleteByExample="false"
               enableSelectByExample="false"
               enableUpdateByExample="false"
               selectByExampleQueryId="false"
        >
            <!--数据库字段命名要么使用camelCase，要么使用snakeCase，禁止混合使用-->
            <property name="useActualColumnNames" value="true"/>
            <domainObjectRenamingRule searchString="^T" replaceString=""/>
        </table>
    </context>
</generatorConfiguration>
