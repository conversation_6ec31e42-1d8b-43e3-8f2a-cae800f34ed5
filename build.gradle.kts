buildscript {
    repositories {
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://nexus.ponderers.cn/repository/maven-public/")
        }
    }
}

plugins {
    id("java")
    id("idea")
    id("java-library")
    id("maven-publish")
}

sourceSets {
    main {
        java.srcDirs("src/main/gen")
    }
    test {
        java.srcDirs("src/test/gen")
    }
}

allprojects {
    group = "org.ponderers.commons"
    version = "3.1.7"
    description = "Ponderers Commons"

    tasks.withType<JavaCompile> {
        sourceCompatibility = JavaVersion.VERSION_21.toString()
        targetCompatibility = JavaVersion.VERSION_21.toString()
    }

    repositories {
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://nexus.ponderers.cn/repository/maven-public/")
        }
    }

    configurations.all {
        resolutionStrategy {
            cacheChangingModulesFor(0, "seconds")
            cacheDynamicVersionsFor(0, "seconds")
        }
    }
}

subprojects {
    apply(plugin = "java")
    apply(plugin = "idea")
    apply(plugin = "java-library")
    apply(plugin = "maven-publish")

    java {
        withSourcesJar()
        //withJavadocJar()
    }
    repositories {
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://nexus.ponderers.cn/repository/maven-public/")
        }
    }
    publishing {
        publications.create<MavenPublication>("PonderersCommon") {
            from(components["java"])
        }
        repositories {
            maven {
                isAllowInsecureProtocol = true
                val releasesRepoUrl = uri("http://nexus.ponderers.cn/repository/maven-releases/")
                val snapshotsRepoUrl = uri("http://nexus.ponderers.cn/repository/maven-snapshots/")
                url = uri(if (version.toString().endsWith("SNAPSHOT")) snapshotsRepoUrl else releasesRepoUrl)
                credentials {
                    username = System.getenv("NEXUS_USERNAME") ?: "Not set"
                    password = System.getenv("NEXUS_PASSWORD") ?: "Not set"
                }
            }
        }
    }
    tasks.getByName<Jar>("jar") {
        enabled = true
        // Remove `plain` postfix from jar file name
        archiveClassifier.set("")
    }
    tasks.withType<JavaCompile>().configureEach {
        options.encoding = "UTF-8"
//        options.compilerArgs.add("--enable-preview")
    }
    tasks.withType<JavaExec>().configureEach {
//        jvmArgs("--enable-preview")
    }
    tasks.withType<Test>().configureEach {
        useJUnitPlatform()
        minHeapSize = "256m"
        maxHeapSize = "2g"
        jvmArgs = listOf("-Xmx2g")
//        jvmArgs = listOf("-Xmx2g", "--enable-preview")
    }
    dependencies {
        // Logging（使用jcl-over-slf4j桥接器无需显示加commons-logging）
        implementation("org.slf4j:slf4j-api:2.0.16")            // 日志 API
        runtimeOnly("org.slf4j:jcl-over-slf4j:2.0.16")          // 日志实现
        implementation("ch.qos.logback:logback-classic:1.5.12") // 兼容 JCL
        // Junit
        testImplementation("org.junit.jupiter:junit-jupiter-api:5.7.2")
        testImplementation("org.junit.jupiter:junit-jupiter-engine:5.7.2")
        // Lombok
        compileOnly("org.projectlombok:lombok:1.18.30")
        annotationProcessor("org.projectlombok:lombok:1.18.30")
        testCompileOnly("org.projectlombok:lombok:1.18.30")
        testAnnotationProcessor("org.projectlombok:lombok:1.18.30")
        // 排除commons-logging依赖
        configurations.all {
            exclude(group = "commons-logging", module = "commons-logging")
        }
    }
}

publishing {
    publications.create<MavenPublication>("PbCommon") {
        from(components["java"])
    }
    repositories {
        maven {
            isAllowInsecureProtocol = true
            val releasesRepoUrl = uri("http://nexus.ponderers.cn/repository/maven-releases/")
            val snapshotsRepoUrl = uri("http://nexus.ponderers.cn/repository/maven-snapshots/")
            url = uri(if (version.toString().endsWith("SNAPSHOT")) snapshotsRepoUrl else releasesRepoUrl)
            credentials {
                username = System.getenv("NEXUS_USERNAME") ?: "Not set"
                password = System.getenv("NEXUS_PASSWORD") ?: "Not set"
            }
        }
    }
}

project(":mybatis-generator") {
    dependencies {
        implementation("org.mybatis.generator:mybatis-generator-core:1.4.2")
        implementation("mysql:mysql-connector-java:8.0.32")
    }
}

project(":commons-util") {
    dependencies {
        // Java Servlet
        compileOnly("jakarta.servlet:jakarta.servlet-api:6.0.0")
        // Apache Commons
        api("org.apache.commons:commons-lang3:3.13.0")
        api("org.apache.commons:commons-collections4:4.4")
        api("org.apache.commons:commons-exec:1.3")
        api("org.apache.commons:commons-pool2:2.11.1")
        api("org.apache.commons:commons-text:1.10.0")
        api("org.apache.commons:commons-rng-simple:1.5")
        api("commons-net:commons-net:3.9.0")
        api("commons-io:commons-io:2.11.0")
        api("commons-codec:commons-codec:1.15")
        api("commons-validator:commons-validator:1.7")
        // Json
        api("com.google.code.gson:gson:2.10.1")
        api("com.jayway.jsonpath:json-path:2.9.0")
        implementation("com.fasterxml.jackson.core:jackson-core:2.18.2")
        implementation("com.fasterxml.jackson.core:jackson-databind:2.18.2")
        implementation("com.fasterxml.jackson.core:jackson-annotations:2.18.2")
        implementation("com.fasterxml.jackson.datatype:jackson-datatype-json-org:2.18.2")
        implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.18.2")
        implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-toml:2.18.2")
        implementation("com.hubspot.jackson:jackson-datatype-protobuf:0.9.17")
        // Bean Validation
        implementation("jakarta.validation:jakarta.validation-api:3.0.2")
        implementation("org.hibernate.validator:hibernate-validator:8.0.0.Final")
        implementation("jakarta.el:jakarta.el-api:5.0.1")
        implementation("org.glassfish.expressly:expressly:5.0.0")
        // Api
        api("org.ini4j:ini4j:0.5.4")
        api("net.lingala.zip4j:zip4j:2.11.4")
        api("com.google.guava:guava:32.1.2-jre")
        // Encrypt
        api("com.auth0:java-jwt:4.3.0")
        implementation("org.bouncycastle:bcprov-jdk18on:1.75")
        implementation("org.bouncycastle:bcmail-jdk18on:1.75")
        // Template
        implementation("com.samskivert:jmustache:1.15")
        // Excel
        implementation("com.alibaba:easyexcel:3.3.2")
        // HttpClient
        api("org.apache.httpcomponents:httpclient:4.5.14")
        api("org.apache.httpcomponents.client5:httpclient5:5.2.1")
        api("com.squareup.okhttp3:okhttp:4.10.0")
        api("net.logstash.logback:logstash-logback-encoder:7.3")
        api("org.aspectj:aspectjrt:1.9.19")
        api("com.github.rholder:guava-retrying:2.0.0")
        api("com.ecwid.consul:consul-api:1.4.5")
        api("com.alibaba:transmittable-thread-local:2.14.2")
        api("org.jsoup:jsoup:1.15.4")
        // Kafka
        implementation("org.apache.kafka:kafka-clients:3.7.0")
        // Micrometer
        implementation("io.micrometer:micrometer-registry-prometheus:1.11.1")
        // Zookeeper
        implementation("org.apache.curator:curator-framework:5.5.0")
        // Netty
        implementation("io.netty:netty-all:4.1.119.Final")
        // Test
        testImplementation("jakarta.servlet:jakarta.servlet-api:6.0.0")
        testImplementation("com.squareup.okhttp3:mockwebserver:4.11.0")
        testImplementation("org.junit.jupiter:junit-jupiter:5.9.2")
        testImplementation("org.junit.platform:junit-platform-launcher:1.9.2")
        testImplementation("org.mockito:mockito-inline:4.11.0")
        testImplementation("org.mockito:mockito-core:4.11.0")
        testImplementation("org.mockito:mockito-junit-jupiter:4.11.0")
        testImplementation("net.bytebuddy:byte-buddy:1.12.21")
        testImplementation("io.undertow:undertow-core:2.2.23.Final")
        testImplementation("io.undertow:undertow-servlet:2.2.23.Final")
        testImplementation("com.github.stefanbirkner:system-rules:1.19.0")
        testImplementation("com.alibaba:fastjson:2.0.25")
        testImplementation("org.openjdk.jmh:jmh-core:1.30")
        testAnnotationProcessor("org.openjdk.jmh:jmh-generator-annprocess:1.30")
    }
}
